# 扫码失败检测逻辑修改说明

## 📋 修改概述

将拍照工序的扫码失败检测逻辑从"DI9超时检测"改为"DI11直接信号检测"。

## 🔄 修改内容

### 原有逻辑（已取消）
- 监视DI9信号触发扫码
- 触发扫码后启动1.2秒超时计时器
- 如果1.2秒内没有收到条码，判定为扫码失败
- 扫码失败时触发停线

### 新逻辑（当前实现）
- 继续监视DI9信号触发扫码
- **新增监视DI11信号**
- 一旦DI11从False变为True，立即判定为扫码失败
- 扫码失败时触发停线

## 🔧 技术实现

### 新增字段
```csharp
private bool _lastDI11State = false; // 上次DI11状态，用于检测扫码失败
private DateTime _lastDI11TriggerTime = DateTime.MinValue; // 上次DI11触发时间
```

### 新增常量
```csharp
private const int DI11_DEBOUNCE_MS = 200; // DI11防抖时间（毫秒）
```

### 核心监控逻辑
```csharp
// DI11监控：检测扫码失败信号
if (inputs.Length > 11)
{
    bool currentDI11State = inputs[11]; // DI11对应数组索引11

    // 检测DI11从False变为True（上升沿触发）
    if (!_lastDI11State && currentDI11State)
    {
        AddStatusMessage("扫码信号Log：DI11触发 - 检测到扫码失败信号");
        
        // 防抖处理
        var now = DateTime.Now;
        if ((now - _lastDI11TriggerTime).TotalMilliseconds > DI11_DEBOUNCE_MS)
        {
            _lastDI11TriggerTime = now;
            
            // 扫码失败，触发停线
            await HandleScanFailureAsync();
        }
    }

    _lastDI11State = currentDI11State;
}
```

### 扫码失败处理方法
```csharp
/// <summary>
/// 处理扫码失败事件（DI11触发）
/// </summary>
private async Task HandleScanFailureAsync()
{
    try
    {
        await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
        {
            AddStatusMessage("❌ 检测到扫码失败信号（DI11），扫码枪可能故障，触发停线");
            QueueStatus = "扫码失败，触发停线";

            // 扫码失败，触发停线
            await StopLineForQualityAsync();
        });
    }
    catch (Exception ex)
    {
        _logService.Error(ex, "处理扫码失败事件时发生异常");
        AddStatusMessage($"❌ 处理扫码失败事件失败: {ex.Message}");
    }
}
```

## 📝 修改的文件

### WorkspaceViewModel.cs
1. **新增字段**：DI11状态跟踪和防抖时间记录
2. **新增常量**：DI11防抖时间常量
3. **修改DI监控逻辑**：添加DI11监控代码
4. **移除超时检测**：删除`CheckScanTimeoutAsync`方法
5. **新增失败处理**：添加`HandleScanFailureAsync`方法
6. **修改触发逻辑**：移除超时检测启动代码

## ⚡ 优势

### 响应更快
- **原逻辑**：最多需要等待1.2秒才能检测到失败
- **新逻辑**：DI11信号触发后立即检测到失败（约100ms内）

### 更准确
- **原逻辑**：基于超时推测，可能误判
- **新逻辑**：基于硬件直接信号，准确性更高

### 更简单
- **原逻辑**：需要维护超时计时器和状态
- **新逻辑**：直接监控信号状态，逻辑更简单

## 🔍 测试要点

1. **DI11信号测试**：确认DI11能正确反映扫码失败状态
2. **防抖测试**：验证200ms防抖时间是否合适
3. **停线测试**：确认DI11触发后能正确停线
4. **恢复测试**：确认停线后能正常恢复
5. **日志测试**：确认相关日志信息正确记录

## 📊 监控信息

### 日志输出
- `扫码信号Log：DI11触发 - 检测到扫码失败信号`
- `❌ 检测到扫码失败信号（DI11），扫码枪可能故障，触发停线`

### 状态更新
- `QueueStatus = "扫码失败，触发停线"`

## 🛠️ 配置说明

### 硬件要求
- 确保PLC或IO模块的DI11连接到扫码枪的失败信号输出
- DI11应该在扫码失败时输出True信号

### 软件配置
- 防抖时间：200ms（可根据实际情况调整`DI11_DEBOUNCE_MS`常量）
- 监控频率：100ms（由DI监控定时器控制）

## 🔄 兼容性

- 保持DI9的产品到位检测功能不变
- 保持现有的停线和恢复机制不变
- 保持现有的日志和状态显示机制不变
- 向后兼容，不影响其他功能

这次修改使扫码失败检测更加及时和准确，提高了生产线的响应速度和可靠性。
