﻿using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data;
using OutletProductionPacking.Data.Repositories;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.WPF.Views;
using OfficeOpenXml;
using OutletProductionPacking.WPF.Services;
using Microsoft.Extensions.Configuration;
using System.IO;
using OutletProductionPacking.ViewModels.UserManagement;
using OutletProductionPacking.ViewModels.ProductManagement;
using OutletProductionPacking.ViewModels;
using OutletProductionPacking.ViewModels.ProductionOrderManage;
using OutletProductionPacking.Services;
using OutletProductionPacking.Utils.Services;
using OutletProductionPacking.ViewModels.Workspace;
using OutletProductionPacking.ViewModels.StatisticalReports;
using System.Linq;
using OutletProductionPacking.WPF.Services;

namespace OutletProductionPacking.WPF
{
    public partial class App : Application
    {
        public new static App Current => (App)Application.Current;
        public IServiceProvider Services { get; }
        private IConfiguration _configuration;
        private PrintServiceDaemon? _printServiceDaemon;
        private WatermarkProcessorService? _watermarkProcessorService;

        public App()
        {
            // 启用程序集绑定失败日志
            AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;

            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();
            Services = ConfigureServices();
        }

        private System.Reflection.Assembly? CurrentDomain_AssemblyResolve(object? sender, ResolveEventArgs args)
        {
            Console.WriteLine($"程序集解析失败: {args.Name}");
            Console.WriteLine($"请求程序集: {args.RequestingAssembly?.FullName}");

            // 尝试从应用程序目录加载
            string assemblyName = args.Name.Split(',')[0];
            string assemblyPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, assemblyName + ".dll");

            Console.WriteLine($"尝试从路径加载: {assemblyPath}");

            if (File.Exists(assemblyPath))
            {
                Console.WriteLine($"找到文件，正在加载: {assemblyPath}");
                return System.Reflection.Assembly.LoadFrom(assemblyPath);
            }

            Console.WriteLine($"文件不存在: {assemblyPath}");
            return null;
        }

        private IServiceProvider ConfigureServices()
        {
            var services = new ServiceCollection();

            // 设置EPPlus许可证
            ExcelPackage.License.SetNonCommercialPersonal("wefef");

            // 配置数据库上下文工厂
            services.AddDbContextFactory<AppDbContext>(options =>
                options.UseMySql(
                    _configuration.GetConnectionString("DefaultConnection"),
                    ServerVersion.AutoDetect(_configuration.GetConnectionString("DefaultConnection")),
                    b => b.MigrationsAssembly("OutletProductionPacking.Data")));

            // 注册仓储
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IProductRepository, ProductRepository>();
            services.AddScoped<IProductionOrderRepository, ProductionOrderRepository>();
            services.AddScoped<IProductionOrderBarcodeRepository, ProductionOrderBarcodeRepository>();
            services.AddScoped<IQualityInspectionRepository, QualityInspectionRepository>();
            services.AddScoped<IProductPhotoRepository, ProductPhotoRepository>();
            services.AddScoped<IBoxLabelSequenceRepository, BoxLabelSequenceRepository>();
            services.AddScoped<IBoxPackageRepository, BoxPackageRepository>();
            services.AddScoped<ICartonLabelSequenceRepository, CartonLabelSequenceRepository>();
            services.AddScoped<ICartonPackageRepository, CartonPackageRepository>();
            services.AddScoped<IBoxQueueRepository, BoxQueueRepository>();
            services.AddScoped<ICartonQueueRepository, CartonQueueRepository>();

            // 注册服务
            services.AddSingleton<IMessageService, MessageService>();
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<IFileService, FileService>();
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IProductService, ProductService>();
            services.AddSingleton<IProductionOrderService, ProductionOrderService>();
            services.AddSingleton<IScaleService, ScaleService>();
            services.AddSingleton<ILogService, NLogService>();
            services.AddSingleton<IWatermarkService, WatermarkService>();
            services.AddSingleton<WatermarkProcessorService>();
            services.AddSingleton<IModbusService, ModbusService>();
            services.AddSingleton<IConfigService>(provider => new ConfigService(_configuration));
            services.AddSingleton<ICameraService, CameraService>();
            services.AddSingleton<IBarTenderService, BarTenderHttpService>();
            services.AddSingleton<IScannerService>(provider => new ScannerService("QualityScanner",provider.GetRequiredService<ILogService>()));
            services.AddSingleton<IScannerService>(provider => new ScannerService("PhotoScanner", provider.GetRequiredService<ILogService>()));
            services.AddSingleton<IProductionDetailQueryService, ProductionDetailQueryService>();

            // 注册视图
            services.AddTransient<MainWindow>();

            // 注册ViewModel
            services.AddTransient<UserListViewModel>();
            services.AddTransient<UserEditViewModel>();
            services.AddTransient<ProductListViewModel>();
            services.AddTransient<ProductEditViewModel>();
            services.AddTransient<ProductionOrderListViewModel>(provider => new ProductionOrderListViewModel(
                provider.GetRequiredService<IProductionOrderService>(),
                provider.GetRequiredService<IDialogService>(),
                provider.GetRequiredService<IMessageService>(),
                provider.GetRequiredService<IProductService>(), provider.GetRequiredService<ILogService>()));
            services.AddTransient<ScaleTestViewModel>();
            services.AddTransient<ModbusTestViewModel>();
            services.AddTransient<ScannerTestViewModel>();
            services.AddTransient<ProductionDetailQueryViewModel>();
            services.AddTransient<WorkspaceViewModel>(provider =>
            {
                var scannerServices = provider.GetServices<IScannerService>().ToList();
                return new WorkspaceViewModel(
                    provider.GetRequiredService<IUserService>(),
                    provider.GetRequiredService<IProductService>(),
                    provider.GetRequiredService<IProductionOrderService>(),
                    provider.GetRequiredService<IMessageService>(),
                    provider.GetRequiredService<IScaleService>(),
                    provider.GetRequiredService<IModbusService>(),
                    scannerServices.FirstOrDefault(s => s.Name == "QualityScanner"),
                    scannerServices.FirstOrDefault(s => s.Name == "PhotoScanner"),
                    provider.GetRequiredService<ICameraService>(),
                    provider.GetRequiredService<IConfigService>(),
                    provider.GetRequiredService<ILogService>(),
                    provider.GetRequiredService<IBarTenderService>(),
                    provider.GetRequiredService<IQualityInspectionRepository>(),
                    provider.GetRequiredService<IProductPhotoRepository>()
                );
            });
            return services.BuildServiceProvider();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            // 启动打印服务守护者
            var logger = Services.GetRequiredService<ILogService>();
            _printServiceDaemon = new PrintServiceDaemon(logger);

            // 启动水印处理服务
            //_watermarkProcessorService = Services.GetRequiredService<WatermarkProcessorService>();
            var mainWindow = Services.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        public static T GetService<T>() where T : class
        {
            if (Current is App app)
            {
                return app.Services.GetRequiredService<T>();
            }
            throw new InvalidOperationException("无法获取服务提供者");
        }

        protected override void OnExit(ExitEventArgs e)
        {
            // 停止水印处理服务
            _watermarkProcessorService?.Dispose();

            // 停止打印服务守护者
            _printServiceDaemon?.Dispose();

            base.OnExit(e);

            if (Services is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }
}
