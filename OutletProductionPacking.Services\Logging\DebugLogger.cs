using System;
using System.IO;
using System.Text;
using System.Threading;

namespace OutletProductionPacking.Services.Logging
{
    public static class DebugLogger
    {
        private static readonly object _lock = new object();
        private static string _logFilePath;

        static DebugLogger()
        {
            try
            {
                string appDataPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "OutletProductionPacking");
                
                if (!Directory.Exists(appDataPath))
                {
                    Directory.CreateDirectory(appDataPath);
                }

                _logFilePath = Path.Combine(appDataPath, "debug_log.txt");
                
                // 如果日志文件超过5MB，创建一个新的
                if (File.Exists(_logFilePath) && new FileInfo(_logFilePath).Length > 5 * 1024 * 1024)
                {
                    string backupPath = Path.Combine(appDataPath, $"debug_log_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                    File.Move(_logFilePath, backupPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化日志失败: {ex.Message}");
                _logFilePath = "debug_log.txt"; // 回退到当前目录
            }
        }

        public static void Log(string message)
        {
            try
            {
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [Thread:{Thread.CurrentThread.ManagedThreadId}] {message}";
                
                // 输出到调试窗口
                System.Diagnostics.Debug.WriteLine(logEntry);
                
                // 写入文件
                lock (_lock)
                {
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"写入日志失败: {ex.Message}");
            }
        }

        public static void LogException(string context, Exception ex)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"[异常] {context}");
            sb.AppendLine($"消息: {ex.Message}");
            sb.AppendLine($"类型: {ex.GetType().FullName}");
            sb.AppendLine($"堆栈: {ex.StackTrace}");
            
            if (ex.InnerException != null)
            {
                sb.AppendLine("内部异常:");
                sb.AppendLine($"消息: {ex.InnerException.Message}");
                sb.AppendLine($"类型: {ex.InnerException.GetType().FullName}");
                sb.AppendLine($"堆栈: {ex.InnerException.StackTrace}");
            }
            
            Log(sb.ToString());
        }
    }
}
