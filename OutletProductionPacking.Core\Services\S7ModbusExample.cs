using OutletProductionPacking.Core.Services;

namespace OutletProductionPacking.Core.Services
{
    /// <summary>
    /// 西门子S7-200 Smart PLC V区读写示例
    /// </summary>
    public class S7ModbusExample
    {
        private readonly IModbusService _modbusService;

        public S7ModbusExample(IModbusService modbusService)
        {
            _modbusService = modbusService;
        }

        /// <summary>
        /// 演示如何读写V0.0地址的各种数据类型
        /// </summary>
        public async Task DemonstrateV0Operations()
        {
            if (!_modbusService.IsConnected)
            {
                Console.WriteLine("请先连接到PLC");
                return;
            }

            Console.WriteLine("=== 西门子S7-200 Smart V0.0 读写示例 ===\n");

            try
            {
                // 1. 读写V0.0的字节值（8位）
                Console.WriteLine("1. 字节操作 (V0.0):");
                
                // 写入字节值 100 到 V0.0
                await _modbusService.WriteVByteAsync(0, 100);
                Console.WriteLine("   写入 V0.0 = 100");
                
                // 读取V0.0的字节值
                byte byteValue = await _modbusService.ReadVByteAsync(0);
                Console.WriteLine($"   读取 V0.0 = {byteValue}");

                // 2. 读写V0.0的字值（16位，占用V0.0和V0.1）
                Console.WriteLine("\n2. 字操作 (V0.0-V0.1):");
                
                // 写入字值 12345 到 V0.0
                await _modbusService.WriteVWordAsync(0, 12345);
                Console.WriteLine("   写入 VW0 = 12345");
                
                // 读取V0.0的字值
                ushort wordValue = await _modbusService.ReadVWordAsync(0);
                Console.WriteLine($"   读取 VW0 = {wordValue}");

                // 3. 读写V0.0的位值
                Console.WriteLine("\n3. 位操作 (V0.0的各个位):");
                
                // 设置V0.0的第0位为True
                await _modbusService.WriteVBitAsync(0, 0, true);
                Console.WriteLine("   设置 V0.0.0 = True");
                
                // 设置V0.0的第7位为True
                await _modbusService.WriteVBitAsync(0, 7, true);
                Console.WriteLine("   设置 V0.0.7 = True");
                
                // 读取V0.0的所有位状态
                Console.WriteLine("   V0.0各位状态:");
                for (byte i = 0; i < 8; i++)
                {
                    bool bitValue = await _modbusService.ReadVBitAsync(0, i);
                    Console.WriteLine($"     V0.0.{i} = {bitValue}");
                }

                // 4. 批量读取多个寄存器
                Console.WriteLine("\n4. 批量读取 (V0.0-V9.1，共10个字):");
                
                ushort[] registers = await _modbusService.ReadHoldingRegistersAsync(0, 10);
                for (int i = 0; i < registers.Length; i++)
                {
                    ushort vAddress = (ushort)(i * 2);
                    Console.WriteLine($"   V{vAddress}.0-V{vAddress + 1}.0 = {registers[i]} (0x{registers[i]:X4})");
                }

                // 5. 批量写入多个寄存器
                Console.WriteLine("\n5. 批量写入:");
                
                ushort[] writeValues = { 1000, 2000, 3000, 4000, 5000 };
                await _modbusService.WriteHoldingRegistersAsync(0, writeValues);
                Console.WriteLine("   批量写入 V0.0-V9.1 = [1000, 2000, 3000, 4000, 5000]");
                
                // 验证写入结果
                ushort[] readBack = await _modbusService.ReadHoldingRegistersAsync(0, 5);
                Console.WriteLine("   验证读取:");
                for (int i = 0; i < readBack.Length; i++)
                {
                    ushort vAddress = (ushort)(i * 2);
                    Console.WriteLine($"     V{vAddress}.0 = {readBack[i]}");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 实际生产中的应用示例
        /// </summary>
        public async Task ProductionExample()
        {
            if (!_modbusService.IsConnected)
            {
                Console.WriteLine("请先连接到PLC");
                return;
            }

            Console.WriteLine("=== 生产应用示例 ===\n");

            try
            {
                // 示例1：控制输出继电器
                Console.WriteLine("1. 控制输出继电器:");
                
                // V0.0控制第1个继电器
                await _modbusService.WriteVBitAsync(0, 0, true);
                Console.WriteLine("   继电器1 开启 (V0.0.0 = True)");
                
                await Task.Delay(2000); // 等待2秒
                
                await _modbusService.WriteVBitAsync(0, 0, false);
                Console.WriteLine("   继电器1 关闭 (V0.0.0 = False)");

                // 示例2：读取传感器状态
                Console.WriteLine("\n2. 读取传感器状态:");
                
                // 假设V1.0存储传感器状态
                byte sensorStatus = await _modbusService.ReadVByteAsync(2); // V1.0对应地址2
                Console.WriteLine($"   传感器状态字节 = {sensorStatus} (二进制: {Convert.ToString(sensorStatus, 2).PadLeft(8, '0')})");
                
                // 检查各个传感器
                for (byte i = 0; i < 8; i++)
                {
                    bool sensorState = await _modbusService.ReadVBitAsync(2, i);
                    Console.WriteLine($"   传感器{i + 1} = {(sensorState ? "触发" : "正常")}");
                }

                // 示例3：设置生产参数
                Console.WriteLine("\n3. 设置生产参数:");
                
                // V2.0存储生产速度（字值）
                ushort productionSpeed = 150;
                await _modbusService.WriteVWordAsync(4, productionSpeed); // V2.0对应地址4
                Console.WriteLine($"   设置生产速度 = {productionSpeed} 件/分钟");
                
                // V4.0存储目标产量（字值）
                ushort targetQuantity = 1000;
                await _modbusService.WriteVWordAsync(8, targetQuantity); // V4.0对应地址8
                Console.WriteLine($"   设置目标产量 = {targetQuantity} 件");

                // 示例4：读取生产计数
                Console.WriteLine("\n4. 读取生产计数:");
                
                // V6.0存储当前产量（字值）
                ushort currentCount = await _modbusService.ReadVWordAsync(12); // V6.0对应地址12
                Console.WriteLine($"   当前产量 = {currentCount} 件");
                
                // 计算完成百分比
                if (targetQuantity > 0)
                {
                    double percentage = (double)currentCount / targetQuantity * 100;
                    Console.WriteLine($"   完成进度 = {percentage:F1}%");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"生产示例执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接到S7-200 Smart PLC
        /// </summary>
        public async Task<bool> ConnectToPLC(string ipAddress = "************", int port = 502)
        {
            Console.WriteLine($"正在连接到西门子S7-200 Smart PLC...");
            Console.WriteLine($"IP地址: {ipAddress}");
            Console.WriteLine($"端口: {port}");
            
            bool connected = await _modbusService.ConnectAsync(ipAddress, port);
            
            if (connected)
            {
                Console.WriteLine("✅ PLC连接成功!");
                return true;
            }
            else
            {
                Console.WriteLine("❌ PLC连接失败!");
                return false;
            }
        }

        /// <summary>
        /// 断开PLC连接
        /// </summary>
        public void DisconnectFromPLC()
        {
            _modbusService.Disconnect();
            Console.WriteLine("PLC连接已断开");
        }
    }
}
