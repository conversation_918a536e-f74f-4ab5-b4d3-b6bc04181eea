@echo off
chcp 65001 >nul
echo ================================
echo   硬件模拟器连接测试脚本
echo ================================
echo.

echo 测试TCP端口连通性...
echo.

echo 测试质量检测扫码枪端口 (2002)...
netstat -an | findstr :2002
if %ERRORLEVEL% EQU 0 (
    echo ✓ 端口2002已监听
) else (
    echo ✗ 端口2002未监听
)

echo.
echo 测试成品拍照扫码枪端口 (2003)...
netstat -an | findstr :2003
if %ERRORLEVEL% EQU 0 (
    echo ✓ 端口2003已监听
) else (
    echo ✗ 端口2003未监听
)

echo.
echo 测试相机端口 (2004)...
netstat -an | findstr :2004
if %ERRORLEVEL% EQU 0 (
    echo ✓ 端口2004已监听
) else (
    echo ✗ 端口2004未监听
)

echo.
echo 测试Modbus TCP端口 (502)...
netstat -an | findstr :502
if %ERRORLEVEL% EQU 0 (
    echo ✓ 端口502已监听
) else (
    echo ✗ 端口502未监听
)

echo.
echo 检查可用串口...
wmic path Win32_SerialPort get DeviceID,Name 2>nul | findstr COM
if %ERRORLEVEL% EQU 0 (
    echo ✓ 发现可用串口
) else (
    echo ℹ 未发现物理串口，电子秤将使用虚拟模式
)

echo.
echo 测试串口COM5...
mode COM5 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ COM5串口可用
) else (
    echo ℹ COM5串口不可用，将使用虚拟模式
)

echo.
echo ================================
echo 测试完成！
echo.
echo 如果所有端口都显示"已监听"，说明模拟器正常运行
echo 如果有端口未监听，请先启动对应的硬件模拟器
echo ================================
echo.
pause
