@echo off
echo ================================
echo Quick COM5 Setup
echo ================================
echo.

REM Check admin rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Need administrator rights!
    echo Right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator rights confirmed
echo.

echo Creating virtual COM5 port...
echo.

REM Method 1: Simple registry method
echo [Step 1] Adding registry entries...
reg add "HKLM\HARDWARE\DEVICEMAP\SERIALCOMM" /v "\Device\VirtualSerial0" /t REG_SZ /d "COM5" /f >nul 2>&1

if %errorLevel% equ 0 (
    echo [OK] Registry entry added
) else (
    echo [ERROR] Failed to add registry entry
)

REM Method 2: Create device entry
echo [Step 2] Creating device entry...
reg add "HKLM\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "DeviceDesc" /t REG_SZ /d "Virtual COM5 Port" /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "HardwareID" /t REG_MULTI_SZ /d "ROOT\PORTS" /f >nul 2>&1
reg add "HKLM\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000" /v "Service" /t REG_SZ /d "Serial" /f >nul 2>&1

echo [OK] Device entry created
echo.

REM Method 3: Refresh hardware
echo [Step 3] Refreshing hardware...
powershell -Command "Get-PnpDevice | Where-Object {$_.Class -eq 'Ports'} | ForEach-Object { try { $_ | Disable-PnpDevice -Confirm:$false; Start-Sleep -Milliseconds 500; $_ | Enable-PnpDevice -Confirm:$false } catch { } }" >nul 2>&1

echo [OK] Hardware refreshed
echo.

REM Test the result
echo [Step 4] Testing COM5...
mode COM5 >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] COM5 is now available!
    echo.
    echo You can now:
    echo 1. Start your scale simulator
    echo 2. Connect your application to COM5
    echo.
    echo COM5 virtual port setup completed successfully!
) else (
    echo [WARNING] COM5 may not be immediately available
    echo.
    echo Try these steps:
    echo 1. Restart your computer
    echo 2. Check Device Manager for COM5
    echo 3. Run your scale simulator - it may create the port automatically
    echo.
)

echo.
echo Press any key to exit...
pause >nul
