@echo off
echo ========================================
echo    BarTender 打印服务启动脚本
echo ========================================
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] 已获得管理员权限
) else (
    echo [WARNING] 建议以管理员身份运行此脚本
)

echo.
echo [INFO] 正在启动 BarTender 打印服务...
echo [INFO] 按 Ctrl+C 可停止服务
echo.

REM 切换到程序目录
cd /d "%~dp0"

REM 启动服务
BarTenderPrintService.exe

echo.
echo [INFO] 服务已停止
pause
