namespace BarTenderPrintService.Models
{
    /// <summary>
    /// 打印响应模型
    /// </summary>
    public class PrintResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// 错误代码（可选）
        /// </summary>
        public string ErrorCode { get; set; }

        /// <summary>
        /// 处理时间戳
        /// </summary>
        public string Timestamp { get; set; }

        public PrintResponse()
        {
            Timestamp = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
        }

        public static PrintResponse CreateSuccess(string requestId, string message = "打印成功")
        {
            return new PrintResponse
            {
                Success = true,
                Message = message,
                RequestId = requestId
            };
        }

        public static PrintResponse CreateError(string requestId, string message, string errorCode = null)
        {
            return new PrintResponse
            {
                Success = false,
                Message = message,
                RequestId = requestId,
                ErrorCode = errorCode
            };
        }
    }
}
