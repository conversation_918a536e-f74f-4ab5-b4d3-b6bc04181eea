using System;
using System.IO.Ports;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using System.IO;

namespace OutletProductionPacking.MockDevices.Services
{
    public class MockScaleService : IDisposable
    {
        private SerialPort? _serialPort;
        private bool _isRunning;
        private readonly string _portName;
        private readonly int _baudRate;
        private decimal _currentWeight;
        private string _currentUnit = "kg";

        public bool IsRunning => _isRunning;
        public string PortName => _portName;
        public int BaudRate => _baudRate;
        public decimal CurrentWeight
        {
            get => _currentWeight;
            set
            {
                _currentWeight = value;
                WeightChanged?.Invoke(this, value);
            }
        }
        public string CurrentUnit
        {
            get => _currentUnit;
            set
            {
                _currentUnit = value;
                UnitChanged?.Invoke(this, value);
            }
        }

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<decimal>? WeightChanged;
        public event EventHandler<string>? UnitChanged;

        public MockScaleService(string portName, int baudRate = 9600)
        {
            _portName = portName;
            _baudRate = baudRate;
        }

        public async Task StartAsync()
        {
            if (_isRunning) return;

            try
            {
                // 检查串口是否存在
                var availablePorts = SerialPort.GetPortNames();
                OnStatusChanged($"系统可用串口: {string.Join(", ", availablePorts)}");

                if (!availablePorts.Contains(_portName))
                {
                    OnStatusChanged($"串口 {_portName} 不存在，尝试创建虚拟串口...");

                    // 尝试创建虚拟串口
                    if (await TryCreateVirtualPortAsync())
                    {
                        OnStatusChanged($"虚拟串口 {_portName} 创建成功，重新检查...");

                        // 等待系统识别新设备
                        await Task.Delay(2000);

                        // 重新获取可用端口
                        availablePorts = SerialPort.GetPortNames();
                        OnStatusChanged($"更新后的可用串口: {string.Join(", ", availablePorts)}");
                    }

                    if (!availablePorts.Contains(_portName))
                    {
                        OnStatusChanged($"无法创建虚拟串口 {_portName}，使用模拟模式运行");
                        _isRunning = true;
                        OnStatusChanged($"电子秤模拟器已启动 (模拟模式, {_portName}, {_baudRate})");

                        // 启动模拟数据发送任务
                        _ = Task.Run(SimulateWeightDataAsync);
                        return;
                    }
                }

                // 尝试打开串口
                _serialPort = new SerialPort(_portName, _baudRate, Parity.None, 8, StopBits.One);
                _serialPort.DataReceived += SerialPort_DataReceived;
                _serialPort.Open();
                _isRunning = true;

                OnStatusChanged($"电子秤模拟器已启动 (串口模式, {_portName}, {_baudRate})");

                // 启动模拟数据发送任务
                _ = Task.Run(SimulateWeightDataAsync);
            }
            catch (Exception ex)
            {
                OnStatusChanged($"串口启动失败: {ex.Message}");
                OnStatusChanged("切换到模拟模式运行");
                _isRunning = true;
                OnStatusChanged($"电子秤模拟器已启动 (模拟模式, {_portName}, {_baudRate})");

                // 启动模拟数据发送任务
                _ = Task.Run(SimulateWeightDataAsync);
            }
        }

        public void Stop()
        {
            if (!_isRunning) return;

            _isRunning = false;

            try
            {
                _serialPort?.Close();
                _serialPort?.Dispose();
                _serialPort = null;

                OnStatusChanged("电子秤模拟器已停止");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"停止电子秤模拟器时发生错误: {ex.Message}");
            }
        }

        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_serialPort == null || !_serialPort.IsOpen) return;

                var data = _serialPort.ReadExisting();
                OnStatusChanged($"收到查询命令: {data.Trim()}");

                // 模拟响应重量数据
                SendWeightResponse();
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理串口数据时发生错误: {ex.Message}");
            }
        }

        private void SendWeightResponse()
        {
            try
            {
                if (_serialPort == null || !_serialPort.IsOpen) return;

                // 模拟Modbus RTU响应格式
                // 这里简化处理，实际应该根据具体的Modbus协议格式
                var weightBytes = BitConverter.GetBytes((float)_currentWeight);
                var response = new byte[8];

                response[0] = 0x01; // 从站地址
                response[1] = 0x03; // 功能码
                response[2] = 0x04; // 数据长度
                Array.Copy(weightBytes, 0, response, 3, 4);

                // 简化CRC计算（实际应该计算正确的CRC）
                response[7] = 0x00;

                _serialPort.Write(response, 0, response.Length);
                OnStatusChanged($"已发送重量数据: {_currentWeight} {_currentUnit}");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"发送重量数据时发生错误: {ex.Message}");
            }
        }

        private async Task SimulateWeightDataAsync()
        {
            var random = new Random();

            while (_isRunning)
            {
                try
                {
                    // 模拟重量变化（在当前重量基础上小幅波动）
                    var variation = (decimal)(random.NextDouble() - 0.5) * 0.1m; // ±0.05kg的波动
                    var newWeight = Math.Max(0, _currentWeight + variation);

                    if (Math.Abs(newWeight - _currentWeight) > 0.01m)
                    {
                        CurrentWeight = Math.Round(newWeight, 1);
                    }

                    await Task.Delay(1000); // 每秒更新一次
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"模拟重量数据时发生错误: {ex.Message}");
                    await Task.Delay(5000); // 出错时等待5秒再重试
                }
            }
        }

        public void SetWeight(decimal weight)
        {
            CurrentWeight = Math.Round(weight, 1);
            OnStatusChanged($"手动设置重量: {CurrentWeight} {CurrentUnit}");

            // 主动发送重量数据到COM5
            if (_serialPort != null && _serialPort.IsOpen)
            {
                SendWeightResponse();
            }

        }

        public void SetUnit(string unit)
        {
            CurrentUnit = unit;
            OnStatusChanged($"设置重量单位: {CurrentUnit}");
        }

        public void AddWeight(decimal increment)
        {
            CurrentWeight = Math.Round(Math.Max(0, CurrentWeight + increment), 1);
            OnStatusChanged($"重量增加 {increment}, 当前重量: {CurrentWeight} {CurrentUnit}");
        }

        public void SimulateStableWeight(decimal weight)
        {
            SetWeight(weight);
            OnStatusChanged($"模拟稳定重量: {weight} {CurrentUnit}");
        }

        public void SimulateWeightFluctuation(decimal baseWeight, decimal range)
        {
            var random = new Random();
            var fluctuation = (decimal)(random.NextDouble() * 2 - 1) * range;
            var newWeight = Math.Max(0, baseWeight + fluctuation);
            CurrentWeight = Math.Round(newWeight, 1);
            OnStatusChanged($"模拟重量波动: {CurrentWeight} {CurrentUnit} (基准: {baseWeight}, 范围: ±{range})");
        }

        private async Task<bool> TryCreateVirtualPortAsync()
        {
            try
            {
                OnStatusChanged("正在尝试创建虚拟串口...");

                // 方法1: 运行虚拟串口创建脚本
                var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Tools", "setup_virtual_com.bat");
                if (File.Exists(scriptPath))
                {
                    OnStatusChanged("找到虚拟串口创建脚本，正在执行...");

                    var startInfo = new ProcessStartInfo
                    {
                        FileName = scriptPath,
                        UseShellExecute = true,
                        Verb = "runas", // 以管理员权限运行
                        CreateNoWindow = true,
                        WindowStyle = ProcessWindowStyle.Hidden
                    };

                    using var process = Process.Start(startInfo);
                    if (process != null)
                    {
                        await process.WaitForExitAsync();
                        if (process.ExitCode == 0)
                        {
                            OnStatusChanged("虚拟串口创建脚本执行成功");
                            return true;
                        }
                    }
                }

                // 方法2: 使用PowerShell创建虚拟串口
                OnStatusChanged("尝试使用PowerShell创建虚拟串口...");
                var psScript = $@"
                    # 添加虚拟串口注册表项
                    try {{
                        New-ItemProperty -Path 'HKLM:\HARDWARE\DEVICEMAP\SERIALCOMM' -Name '\Device\VirtualSerial0' -Value '{_portName}' -PropertyType String -Force
                        Write-Host '虚拟串口注册表项创建成功'
                        exit 0
                    }} catch {{
                        Write-Host '创建失败:' $_.Exception.Message
                        exit 1
                    }}
                ";

                var psStartInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-ExecutionPolicy Bypass -Command \"{psScript}\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using var psProcess = Process.Start(psStartInfo);
                if (psProcess != null)
                {
                    await psProcess.WaitForExitAsync();
                    if (psProcess.ExitCode == 0)
                    {
                        OnStatusChanged("PowerShell虚拟串口创建成功");
                        return true;
                    }
                }

                OnStatusChanged("所有虚拟串口创建方法都失败");
                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"创建虚拟串口时发生错误: {ex.Message}");
                return false;
            }
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, $"[电子秤] {message}");
        }

        public void Dispose()
        {
            Stop();
        }
    }
}
