<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.ImportProgressDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.Dialogs"
        mc:Ignorable="d"
        Title="导入进度"
        Height="300"
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="ToolWindow">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="{Binding StatusText}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"
                   FontWeight="Bold"/>

        <!-- 详细信息 -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" Grid.Column="0" Text="订单号：" Margin="0,0,5,5" FontWeight="Bold"/>
            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentOrderNumber}" Margin="0,0,0,5"/>

            <TextBlock Grid.Row="1" Grid.Column="0" Text="产品编码：" Margin="0,0,5,5" FontWeight="Bold"/>
            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentProductCode}" Margin="0,0,0,5"/>

            <TextBlock Grid.Row="2" Grid.Column="0" Text="产品名称：" Margin="0,0,5,5" FontWeight="Bold"/>
            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentProductName}" Margin="0,0,0,5"/>

            <TextBlock Grid.Row="3" Grid.Column="0" Text="条码进度：" Margin="0,0,5,0" FontWeight="Bold"/>
            <TextBlock Grid.Row="3" Grid.Column="1" Margin="0,0,0,0">
                <Run Text="{Binding CurrentBarcodeIndex, Mode=OneWay}"/>
                <Run Text="/"/>
                <Run Text="{Binding CurrentBarcodeCount, Mode=OneWay}"/>
            </TextBlock>
        </Grid>

        <ProgressBar Grid.Row="2"
                     Value="{Binding Progress}"
                     Maximum="100"
                     Height="20"
                     Margin="0,0,0,10"/>

        <TextBlock Grid.Row="3"
                   Text="{Binding Progress, StringFormat={}总进度: {0}%}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"
                   FontWeight="Bold"/>

        <TextBlock Grid.Row="4"
                   Text="{Binding ImportStatus}"
                   HorizontalAlignment="Center"
                   Margin="0,10,0,10"
                   FontWeight="Bold"
                   TextWrapping="Wrap"/>

        <TextBlock Grid.Row="5"
                   Text="注意：导入过程中请勿关闭此窗口，导入完成后可点击关闭按钮"
                   HorizontalAlignment="Center"
                   Margin="0,5,0,10"
                   Foreground="#FF6347"
                   TextWrapping="Wrap"/>

        <StackPanel Grid.Row="8" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
            <Button Content="关闭"
                    IsEnabled="{Binding IsCompleted}"
                    Click="CloseButton_Click"
                    Padding="20,5"
                    Margin="0,0,10,0"/>
            <Button Content="强制关闭"
                    Click="ForceCloseButton_Click"
                    Padding="20,5"
                    Background="#FFE0E0E0"
                    Foreground="#FF6347"/>
        </StackPanel>
    </Grid>
</Window>
