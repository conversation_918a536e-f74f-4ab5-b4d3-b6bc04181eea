using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OutletProductionPacking.Data.Repositories
{
    public class BoxPackageRepository : IBoxPackageRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public BoxPackageRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<BoxPackage> GetByIdAsync(int id)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxPackages.FindAsync(id);
        }

        public async Task<BoxPackage> GetByBoxNumberAsync(string boxNumber)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxPackages
                .FirstOrDefaultAsync(p => p.BoxNumber == boxNumber);
        }

        public async Task<List<BoxPackage>> GetByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxPackages
                .Where(p => p.OrderId == orderId)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();
        }

        public async Task<BoxPackage> AddAsync(BoxPackage package)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            await context.BoxPackages.AddAsync(package);
            await context.SaveChangesAsync();
            return package;
        }

        public async Task<List<string>> GetBoxNumbersByOrderIdAsync(int orderId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.BoxPackages
                .Where(p => p.OrderId == orderId)
                .Select(p => p.BoxNumber)
                .OrderByDescending(p => p)
                .ToListAsync();
        }
    }
}
