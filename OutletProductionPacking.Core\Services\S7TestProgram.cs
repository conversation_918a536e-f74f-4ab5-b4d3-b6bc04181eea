using OutletProductionPacking.Core.Services;

namespace OutletProductionPacking.Core.Services
{
    /// <summary>
    /// 西门子S7-200 Smart测试程序
    /// </summary>
    public class S7TestProgram
    {
        /// <summary>
        /// 主测试方法
        /// </summary>
        public static async Task RunTestAsync()
        {
            Console.WriteLine("=== 西门子S7-200 Smart Modbus测试程序 ===\n");

            // 创建Modbus服务实例
            var modbusService = new ModbusService();
            var s7Example = new S7ModbusExample(modbusService);

            try
            {
                // 1. 连接到PLC
                Console.WriteLine("步骤1: 连接PLC");
                bool connected = await s7Example.ConnectToPLC("************", 502);
                
                if (!connected)
                {
                    Console.WriteLine("无法连接到PLC，请检查:");
                    Console.WriteLine("1. PLC的IP地址是否正确");
                    Console.WriteLine("2. PLC是否开启了Modbus TCP服务");
                    Console.WriteLine("3. 网络连接是否正常");
                    return;
                }

                Console.WriteLine("\n按任意键继续...");
                Console.ReadKey();

                // 2. 基本V0.0操作演示
                Console.WriteLine("\n步骤2: V0.0基本操作演示");
                await s7Example.DemonstrateV0Operations();

                Console.WriteLine("\n按任意键继续...");
                Console.ReadKey();

                // 3. 生产应用示例
                Console.WriteLine("\n步骤3: 生产应用示例");
                await s7Example.ProductionExample();

                Console.WriteLine("\n按任意键继续...");
                Console.ReadKey();

                // 4. 高级操作示例
                Console.WriteLine("\n步骤4: 高级操作示例");
                await AdvancedOperationsExample(modbusService);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            finally
            {
                // 断开连接
                s7Example.DisconnectFromPLC();
                Console.WriteLine("\n测试完成，按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 高级操作示例
        /// </summary>
        private static async Task AdvancedOperationsExample(IModbusService modbusService)
        {
            Console.WriteLine("=== 高级操作示例 ===\n");

            try
            {
                // 1. 数据类型转换示例
                Console.WriteLine("1. 数据类型转换:");
                
                // 将浮点数写入V区（占用2个字）
                float floatValue = 123.45f;
                byte[] floatBytes = BitConverter.GetBytes(floatValue);
                ushort[] floatWords = new ushort[2];
                floatWords[0] = BitConverter.ToUInt16(floatBytes, 0);
                floatWords[1] = BitConverter.ToUInt16(floatBytes, 2);
                
                await modbusService.WriteHoldingRegistersAsync(10, floatWords); // 写入V10.0
                Console.WriteLine($"   写入浮点数 {floatValue} 到 V10.0");
                
                // 读取并转换回浮点数
                ushort[] readFloatWords = await modbusService.ReadHoldingRegistersAsync(10, 2);
                byte[] readFloatBytes = new byte[4];
                BitConverter.GetBytes(readFloatWords[0]).CopyTo(readFloatBytes, 0);
                BitConverter.GetBytes(readFloatWords[1]).CopyTo(readFloatBytes, 2);
                float readFloatValue = BitConverter.ToSingle(readFloatBytes, 0);
                Console.WriteLine($"   读取浮点数 = {readFloatValue}");

                // 2. 字符串操作示例
                Console.WriteLine("\n2. 字符串操作:");
                
                string text = "HELLO";
                byte[] textBytes = System.Text.Encoding.ASCII.GetBytes(text.PadRight(10, '\0')); // 补齐到10字节
                ushort[] textWords = new ushort[5]; // 10字节 = 5个字
                
                for (int i = 0; i < textWords.Length; i++)
                {
                    textWords[i] = (ushort)((textBytes[i * 2 + 1] << 8) | textBytes[i * 2]);
                }
                
                await modbusService.WriteHoldingRegistersAsync(20, textWords); // 写入V20.0
                Console.WriteLine($"   写入字符串 \"{text}\" 到 V20.0");
                
                // 读取字符串
                ushort[] readTextWords = await modbusService.ReadHoldingRegistersAsync(20, 5);
                byte[] readTextBytes = new byte[10];
                
                for (int i = 0; i < readTextWords.Length; i++)
                {
                    readTextBytes[i * 2] = (byte)(readTextWords[i] & 0xFF);
                    readTextBytes[i * 2 + 1] = (byte)((readTextWords[i] >> 8) & 0xFF);
                }
                
                string readText = System.Text.Encoding.ASCII.GetString(readTextBytes).TrimEnd('\0');
                Console.WriteLine($"   读取字符串 = \"{readText}\"");

                // 3. 位操作批量处理
                Console.WriteLine("\n3. 位操作批量处理:");
                
                // 设置V30.0的多个位
                byte bitPattern = 0b10101010; // 二进制模式
                await modbusService.WriteVByteAsync(60, bitPattern); // V30.0对应地址60
                Console.WriteLine($"   写入位模式 {Convert.ToString(bitPattern, 2).PadLeft(8, '0')} 到 V30.0");
                
                // 读取并显示每一位
                Console.WriteLine("   V30.0各位状态:");
                for (byte i = 0; i < 8; i++)
                {
                    bool bitValue = await modbusService.ReadVBitAsync(60, i);
                    Console.WriteLine($"     V30.0.{i} = {(bitValue ? "1" : "0")}");
                }

                // 4. 性能测试
                Console.WriteLine("\n4. 性能测试:");
                
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                int testCount = 100;
                
                for (int i = 0; i < testCount; i++)
                {
                    await modbusService.ReadVWordAsync(0); // 读取V0.0
                }
                
                stopwatch.Stop();
                double avgTime = stopwatch.ElapsedMilliseconds / (double)testCount;
                Console.WriteLine($"   读取V0.0 {testCount}次，平均耗时: {avgTime:F2}ms");

                // 5. 错误处理示例
                Console.WriteLine("\n5. 错误处理示例:");
                
                try
                {
                    // 尝试读取超出范围的地址
                    await modbusService.ReadHoldingRegistersAsync(65535, 1);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   预期错误: {ex.Message}");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"高级操作示例失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 快速测试方法 - 仅测试V0.0读写
        /// </summary>
        public static async Task QuickTestV0Async(string plcIP = "************")
        {
            Console.WriteLine("=== V0.0快速测试 ===\n");

            var modbusService = new ModbusService();

            try
            {
                // 连接
                Console.WriteLine($"连接到PLC: {plcIP}");
                bool connected = await modbusService.ConnectAsync(plcIP, 502);
                
                if (!connected)
                {
                    Console.WriteLine("连接失败!");
                    return;
                }

                Console.WriteLine("连接成功!");

                // 测试V0.0字节读写
                Console.WriteLine("\n测试V0.0字节读写:");
                
                byte testValue = 42;
                await modbusService.WriteVByteAsync(0, testValue);
                Console.WriteLine($"写入: V0.0 = {testValue}");
                
                byte readValue = await modbusService.ReadVByteAsync(0);
                Console.WriteLine($"读取: V0.0 = {readValue}");
                
                if (readValue == testValue)
                {
                    Console.WriteLine("✅ V0.0读写测试成功!");
                }
                else
                {
                    Console.WriteLine("❌ V0.0读写测试失败!");
                }

                // 测试V0.0位读写
                Console.WriteLine("\n测试V0.0位读写:");
                
                await modbusService.WriteVBitAsync(0, 0, true);
                Console.WriteLine("写入: V0.0.0 = True");
                
                bool bitValue = await modbusService.ReadVBitAsync(0, 0);
                Console.WriteLine($"读取: V0.0.0 = {bitValue}");
                
                if (bitValue)
                {
                    Console.WriteLine("✅ V0.0位读写测试成功!");
                }
                else
                {
                    Console.WriteLine("❌ V0.0位读写测试失败!");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
            }
            finally
            {
                modbusService.Disconnect();
                Console.WriteLine("\n测试完成");
            }
        }
    }
}
