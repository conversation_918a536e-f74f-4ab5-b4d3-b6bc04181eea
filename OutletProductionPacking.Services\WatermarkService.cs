using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Utils.Services;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;

namespace OutletProductionPacking.Services
{
    public class WatermarkService : IWatermarkService
    {
        private readonly ILogService _logger;

        public WatermarkService(ILogService logger)
        {
            _logger = logger;
        }

        public async Task<bool> AddWatermarkAsync(string imagePath, string watermarkText)
        {
            try
            {
                if (!File.Exists(imagePath))
                {
                    _logger.Warn($"图片文件不存在: {imagePath}");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(watermarkText))
                {
                    _logger.Warn("水印文本为空");
                    return false;
                }

                await Task.Run(() =>
                {
                    using (var originalImage = Image.FromFile(imagePath))
                    {
                        using (var watermarkedImage = new Bitmap(originalImage.Width, originalImage.Height))
                        {
                            using (var graphics = Graphics.FromImage(watermarkedImage))
                            {
                                // 设置高质量渲染
                                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                                graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

                                // 绘制原始图片
                                graphics.DrawImage(originalImage, 0, 0);

                                // 计算字体大小，使文本宽度占图片宽度的80%
                                var fontSize = CalculateFontSize(graphics, watermarkText, originalImage.Width * 0.8f);
                                
                                using (var font = new Font("Arial", fontSize, FontStyle.Bold))
                                {
                                    // 创建70%透明的黄色画刷
                                    using (var brush = new SolidBrush(Color.FromArgb(179, Color.Yellow))) // 179 = 255 * 0.7
                                    {
                                        // 测量文本大小
                                        var textSize = graphics.MeasureString(watermarkText, font);
                                        
                                        // 计算文本位置（居中）
                                        var x = (originalImage.Width - textSize.Width) / 2;
                                        var y = (originalImage.Height - textSize.Height) / 2;

                                        // 绘制水印文本
                                        graphics.DrawString(watermarkText, font, brush, x, y);
                                    }
                                }
                            }

                            // 保存带水印的图片，覆盖原文件
                            var tempPath = imagePath + ".tmp";
                            watermarkedImage.Save(tempPath, ImageFormat.Jpeg);
                            
                            // 替换原文件
                            File.Delete(imagePath);
                            File.Move(tempPath, imagePath);
                        }
                    }
                });

                _logger.Info($"成功为图片添加水印: {imagePath}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"为图片添加水印失败: {imagePath}");
                return false;
            }
        }

        /// <summary>
        /// 计算合适的字体大小，使文本宽度达到指定宽度
        /// </summary>
        private float CalculateFontSize(Graphics graphics, string text, float targetWidth)
        {
            float fontSize = 12f;
            float maxFontSize = 200f;
            float minFontSize = 8f;

            while (fontSize <= maxFontSize)
            {
                using (var font = new Font("Arial", fontSize, FontStyle.Bold))
                {
                    var textSize = graphics.MeasureString(text, font);
                    if (textSize.Width >= targetWidth)
                    {
                        return Math.Max(fontSize - 1, minFontSize);
                    }
                }
                fontSize += 1f;
            }

            return maxFontSize;
        }
    }
}
