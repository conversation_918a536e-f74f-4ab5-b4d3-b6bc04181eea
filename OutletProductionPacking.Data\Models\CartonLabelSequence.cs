using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 大箱贴序号管理表
    /// </summary>
    [Table("CartonLabelSequences")]
    public class CartonLabelSequence
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 日期（YYMMDD格式）
        /// </summary>
        [Required]
        [StringLength(6)]
        public string DateCode { get; set; } = string.Empty;

        /// <summary>
        /// 当前序号（4位，0001-9999）
        /// </summary>
        public int CurrentSequence { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
