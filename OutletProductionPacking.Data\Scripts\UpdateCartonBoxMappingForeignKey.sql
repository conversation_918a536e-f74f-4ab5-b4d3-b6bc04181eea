-- 修复 CartonBoxMapping 表的外键关系
-- 解决 Entity Framework 关系配置错误

-- 1. 添加新的外键字段
ALTER TABLE CartonBoxMappings 
ADD COLUMN CartonPackageId INT NOT NULL DEFAULT 0;

-- 2. 更新现有数据，设置正确的外键值
UPDATE CartonBoxMappings cbm
INNER JOIN CartonPackages cp ON cbm.CartonNumber = cp.CartonNumber
SET cbm.CartonPackageId = cp.Id;

-- 3. 创建外键约束
ALTER TABLE CartonBoxMappings
ADD CONSTRAINT FK_CartonBoxMappings_CartonPackages
FOREIGN KEY (CartonPackageId) REFERENCES CartonPackages(Id)
ON DELETE CASCADE;

-- 4. 创建新的索引
CREATE INDEX IX_CartonBoxMappings_CartonPackageId ON CartonBoxMappings(CartonPackageId);

-- 5. 验证数据完整性
SELECT 
    cbm.Id,
    cbm.CartonPackageId,
    cbm.CartonNumber,
    cbm.BoxNumber,
    cp.Id as CartonPackageActualId,
    cp.CartonNumber as CartonPackageActualNumber
FROM CartonBoxMappings cbm
LEFT JOIN CartonPackages cp ON cbm.CartonPackageId = cp.Id
WHERE cbm.CartonPackageId = 0 OR cp.Id IS NULL;

-- 如果上面的查询返回任何行，说明数据有问题，需要手动修复
