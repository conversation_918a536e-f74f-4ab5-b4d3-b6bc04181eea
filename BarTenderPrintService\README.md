# BarTender 打印服务

## 📋 概述

这是一个基于 .NET Framework 4.8 的 BarTender 2022 打印服务中间件，用于解决 BarTender 2022 与 .NET 8.0 的兼容性问题。

## 🏗️ 架构

```
┌─────────────────────┐    HTTP API    ┌──────────────────────┐    COM API    ┌─────────────────┐
│  生产系统(.NET 8)   │ ──────────────► │ 打印服务(.NET Fx)    │ ─────────────► │  BarTender 2022 │
│                     │                │                      │               │                 │
│ - 主业务逻辑        │                │ - HTTP Web API       │               │ - 标签模板      │
│ - 界面交互          │                │ - BarTender 集成     │               │ - 打印引擎      │
│ - 数据管理          │                │ - 打印队列管理       │               │                 │
└─────────────────────┘                └──────────────────────┘               └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- Windows 10/11 或 Windows Server 2016+
- .NET Framework 4.8
- BarTender 2022 已安装
- Visual Studio 2019+ 或 MSBuild

### 2. 编译项目

```bash
# 使用 Visual Studio
1. 打开 BarTenderPrintService.csproj
2. 还原 NuGet 包
3. 编译项目 (Ctrl+Shift+B)

# 使用命令行
msbuild BarTenderPrintService.csproj /p:Configuration=Release
```

### 3. 配置

编辑 `App.config` 文件：

```xml
<appSettings>
    <add key="ServerPort" value="8080" />
    <add key="TemplateBasePath" value="C:\PrintTemplates\" />
    <add key="LogLevel" value="Info" />
</appSettings>
```

### 4. 准备模板

1. 创建模板目录：`C:\PrintTemplates\`
2. 将 BarTender 模板文件 (*.btw) 复制到该目录
3. 确保模板中的变量名与API调用中的参数名一致

### 5. 启动服务

```bash
# 直接运行
BarTenderPrintService.exe

# 或者作为Windows服务运行（需要额外配置）
```

## 📡 API 接口

### 打印标签

**POST** `/print`

```json
{
    "TemplateName": "BoxTemplate.btw",
    "Parameters": {
        "产品型号": "ABC123",
        "生产日期": "2025-05-30",
        "条码": "123456789"
    },
    "PrinterName": "Label Printer",
    "Copies": 1,
    "RequestId": "uuid-string"
}
```

**响应：**
```json
{
    "Success": true,
    "Message": "打印成功，份数: 1",
    "RequestId": "uuid-string",
    "Timestamp": "2025-05-30 10:30:00.123"
}
```

### 查询服务状态

**GET** `/status`

```json
{
    "isReady": true,
    "templatePath": "C:\\PrintTemplates\\",
    "timestamp": "2025-05-30 10:30:00"
}
```

### 获取打印机列表

**GET** `/printers`

```json
{
    "printers": [
        "Microsoft Print to PDF",
        "Label Printer",
        "Zebra ZT230"
    ]
}
```

### 获取模板列表

**GET** `/templates`

```json
{
    "templates": [
        "BoxTemplate.btw",
        "CartonTemplate.btw"
    ]
}
```

## 🔧 .NET 8 客户端集成

在你的 .NET 8 项目中使用 `BarTenderHttpService`：

```csharp
// 注册服务
services.AddSingleton<IBarTenderService, BarTenderHttpService>();

// 配置文件 appsettings.json
{
    "BarTender": {
        "PrintServerHost": "localhost",
        "PrintServerPort": "8080",
        "BoxLabelTemplate": "BoxTemplate.btw",
        "CartonLabelTemplate": "CartonTemplate.btw"
    }
}

// 使用示例
var barTenderService = serviceProvider.GetService<IBarTenderService>();
var parameters = new Dictionary<string, string>
{
    ["产品型号"] = "ABC123",
    ["生产日期"] = DateTime.Now.ToString("yyyy-MM-dd")
};

bool success = await barTenderService.PrintBoxLabelAsync(parameters, 1);
```

## 🛠️ 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认 BarTender 2022 已正确安装
   - 检查 .NET Framework 4.8 是否安装

2. **打印失败**
   - 验证模板文件路径是否正确
   - 检查模板中的变量名是否匹配
   - 确认打印机驱动已安装

3. **连接超时**
   - 检查防火墙设置
   - 确认服务器地址和端口配置正确

### 日志

服务会在控制台输出详细的日志信息，包括：
- 服务启动状态
- 打印请求处理
- 错误信息和异常

## 📝 开发说明

### 扩展功能

1. **添加新的API接口**：在 `PrintServer.cs` 中添加新的路由处理
2. **自定义打印逻辑**：修改 `BarTenderService.cs` 中的打印方法
3. **增加认证**：在 `ProcessRequest` 方法中添加身份验证逻辑

### 部署为Windows服务

可以使用 TopShelf 或其他工具将应用程序部署为Windows服务，实现开机自启动。

## 📄 许可证

本项目仅供学习和内部使用。
