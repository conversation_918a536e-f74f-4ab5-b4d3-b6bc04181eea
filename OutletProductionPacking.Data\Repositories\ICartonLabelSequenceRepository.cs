using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface ICartonLabelSequenceRepository
    {
        /// <summary>
        /// 获取或创建指定日期的序号记录
        /// </summary>
        /// <param name="dateCode">日期码（YYMMDD）</param>
        /// <returns>序号记录</returns>
        Task<CartonLabelSequence> GetOrCreateAsync(string dateCode);

        /// <summary>
        /// 获取下一个序号并更新
        /// </summary>
        /// <param name="dateCode">日期码（YYMMDD）</param>
        /// <returns>下一个序号（4位）</returns>
        Task<int> GetNextSequenceAsync(string dateCode);
    }
}
