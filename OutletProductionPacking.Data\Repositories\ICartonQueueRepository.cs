using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface ICartonQueueRepository
    {
        /// <summary>
        /// 添加盒号到大箱队列
        /// </summary>
        Task<CartonQueue> AddAsync(CartonQueue cartonQueue);

        /// <summary>
        /// 根据订单ID获取大箱队列中的盒号
        /// </summary>
        Task<List<CartonQueue>> GetByOrderIdAsync(int orderId);

        /// <summary>
        /// 根据订单ID获取大箱队列中的盒号列表（仅盒号字符串）
        /// </summary>
        Task<List<string>> GetBoxNumbersByOrderIdAsync(int orderId);

        /// <summary>
        /// 检查盒号是否已在大箱队列中
        /// </summary>
        Task<bool> ExistsByBoxNumberAsync(string boxNumber);

        /// <summary>
        /// 根据盒号列表批量删除
        /// </summary>
        Task DeleteByBoxNumbersAsync(List<string> boxNumbers);

        /// <summary>
        /// 根据订单ID删除所有大箱队列盒号
        /// </summary>
        Task DeleteByOrderIdAsync(int orderId);

        /// <summary>
        /// 获取指定数量的盒号（用于装箱）
        /// </summary>
        Task<List<string>> GetBoxNumbersForPackingAsync(int orderId, int count = 2);

        /// <summary>
        /// 获取所有已装箱的盒号
        /// </summary>
        Task<List<string>> GetUsedBoxNumbersAsync();
    }
}
