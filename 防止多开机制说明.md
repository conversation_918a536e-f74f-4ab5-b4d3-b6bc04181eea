# 防止多开机制说明

## 📋 功能概述

为主软件添加了防止多开的机制，确保同一时间只能运行一个应用程序实例。

## 🔧 技术实现

### 核心机制
使用 **System.Threading.Mutex（互斥锁）** 来实现单实例运行控制。

### 实现原理
1. **应用程序启动时**：创建一个具有唯一名称的互斥锁
2. **检查互斥锁状态**：如果互斥锁已被其他实例占用，说明程序已在运行
3. **处理重复启动**：显示提示信息并退出当前启动尝试
4. **正常启动**：如果没有其他实例，继续正常启动流程
5. **应用程序退出时**：释放互斥锁，允许新实例启动

## 📝 代码实现

### 新增字段
```csharp
private Mutex? _applicationMutex;
```

### 启动检查逻辑
```csharp
protected override void OnStartup(StartupEventArgs e)
{
    // 检查是否已有实例在运行
    const string mutexName = "OutletProductionPacking_SingleInstance_Mutex";
    bool createdNew;
    
    _applicationMutex = new Mutex(true, mutexName, out createdNew);
    
    if (!createdNew)
    {
        // 已有实例在运行，显示提示并退出
        MessageBox.Show(
            "应用程序已在运行中！\n\n请检查任务栏或系统托盘，如果找不到窗口，请结束进程后重新启动。",
            "出口生产包装系统",
            MessageBoxButton.OK,
            MessageBoxImage.Warning);
        
        // 释放互斥锁并退出
        _applicationMutex?.ReleaseMutex();
        _applicationMutex?.Dispose();
        _applicationMutex = null;
        
        // 退出应用程序
        Environment.Exit(0);
        return;
    }

    try
    {
        // 正常启动流程...
    }
    catch (Exception ex)
    {
        // 启动失败时释放互斥锁
        _applicationMutex?.ReleaseMutex();
        _applicationMutex?.Dispose();
        _applicationMutex = null;
        
        MessageBox.Show(
            $"应用程序启动失败：\n\n{ex.Message}",
            "出口生产包装系统",
            MessageBoxButton.OK,
            MessageBoxImage.Error);
        
        Environment.Exit(1);
    }
}
```

### 退出清理逻辑
```csharp
protected override void OnExit(ExitEventArgs e)
{
    try
    {
        // 其他清理工作...

        // 释放单实例互斥锁
        if (_applicationMutex != null)
        {
            try
            {
                _applicationMutex.ReleaseMutex();
                _applicationMutex.Dispose();
                _applicationMutex = null;
            }
            catch (Exception ex)
            {
                // 记录日志但不阻止退出
                Console.WriteLine($"释放互斥锁时发生异常: {ex.Message}");
            }
        }

        base.OnExit(e);
    }
    catch (Exception ex)
    {
        // 确保应用程序能够退出
        Console.WriteLine($"应用程序退出时发生异常: {ex.Message}");
        Environment.Exit(1);
    }
}
```

## 🎯 功能特性

### 1. **智能检测**
- 使用全局唯一的互斥锁名称：`OutletProductionPacking_SingleInstance_Mutex`
- 跨用户会话检测（同一台机器上的不同用户）
- 进程崩溃后自动释放锁

### 2. **用户友好提示**
- 清晰的提示信息：告知用户程序已在运行
- 提供解决建议：检查任务栏或结束进程
- 使用警告图标，不会误导用户

### 3. **异常处理**
- 启动失败时自动释放互斥锁
- 退出时安全释放资源
- 异常情况下强制退出，避免僵死

### 4. **资源管理**
- 自动释放互斥锁资源
- 防止内存泄漏
- 确保系统资源正确清理

## 📊 使用场景

### 正常情况
1. **首次启动**：创建互斥锁，正常启动应用程序
2. **重复启动**：检测到互斥锁已存在，显示提示并退出
3. **正常退出**：释放互斥锁，允许下次启动

### 异常情况
1. **进程崩溃**：操作系统自动释放互斥锁
2. **强制结束**：互斥锁自动释放
3. **启动异常**：手动释放互斥锁并退出

## ⚠️ 注意事项

### 1. **互斥锁名称**
- 使用应用程序特有的名称，避免与其他程序冲突
- 名称包含应用程序标识：`OutletProductionPacking_SingleInstance_Mutex`

### 2. **权限问题**
- 在某些受限环境下可能需要管理员权限
- 不同用户账户下的实例会被视为不同实例

### 3. **网络部署**
- 仅限制本机多开，不影响网络上其他机器的运行
- 适用于单机部署的生产环境

### 4. **调试模式**
- 开发调试时可能需要临时禁用此功能
- 可通过编译条件或配置文件控制

## 🔍 测试验证

### 测试步骤
1. **正常启动测试**：启动应用程序，验证正常运行
2. **重复启动测试**：再次启动应用程序，验证提示信息
3. **强制结束测试**：强制结束进程，验证能重新启动
4. **正常退出测试**：正常关闭程序，验证能重新启动

### 预期结果
- ✅ 首次启动成功
- ✅ 重复启动显示提示并退出
- ✅ 进程结束后能正常重启
- ✅ 正常退出后能正常重启

## 🎯 优势

1. **防止数据冲突**：避免多个实例同时操作数据库
2. **资源保护**：防止重复占用硬件资源（如串口、摄像头）
3. **用户体验**：避免用户困惑和误操作
4. **系统稳定**：减少资源竞争和冲突
5. **简单可靠**：使用系统级机制，稳定性高

这个防止多开机制确保了生产环境下的系统稳定性和数据安全性。
