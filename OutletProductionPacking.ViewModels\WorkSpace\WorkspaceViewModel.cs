﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Models;
using OutletProductionPacking.Data.Repositories;
using OutletProductionPacking.Utils.Services;
using System.Collections.ObjectModel;
using System.Windows.Threading;

namespace OutletProductionPacking.ViewModels.Workspace
{
    public partial class WorkspaceViewModel : ObservableObject, IDisposable
    {
        private readonly IUserService _userService;
        private readonly IProductService _productService;
        private readonly IProductionOrderService _productionOrderService;
        private readonly IMessageService _messageService;
        private readonly IScaleService _scaleService;
        private readonly IModbusService _modbusService;
        private readonly IScannerService _scannerService1; // 质量检测扫码枪
        private readonly IScannerService _scannerService2; // 成品拍照扫码枪
        private readonly ICameraService _cameraService;    // 相机服务
        private readonly IConfigService _configService;    // 配置服务
        private readonly ILogService _logService;
        private readonly IBarTenderService _barTenderService; // BarTender打印服务
        private readonly IQualityInspectionRepository _qualityInspectionRepository; // 质量检测记录仓储
        private readonly IProductPhotoRepository _productPhotoRepository; // 成品拍照记录仓储

        #region 基础信息与硬件状态

        // 操作工登录
        [ObservableProperty]
        private string _username = string.Empty;

        [ObservableProperty]
        private string _password = string.Empty;

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(PrintBoxLabelCommand))]
        private User? _currentUser;

        [ObservableProperty]
        private bool _isLoggedIn;

        // 订单选择
        [ObservableProperty]
        private ObservableCollection<ProductionOrder> _availableOrders = new();

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(PrintBoxLabelCommand))]
        private ProductionOrder? _selectedOrder;

        [ObservableProperty]
        [NotifyCanExecuteChangedFor(nameof(PrintBoxLabelCommand))]
        private Product? _currentProduct;

        // 生产模式选择
        [ObservableProperty]
        private ObservableCollection<string> _productionModes = new() { "正常生产", "包装返修" };

        [ObservableProperty]
        private string _selectedProductionMode = "正常生产";

        [ObservableProperty]
        private bool _isPackageRepairMode = false;

        [ObservableProperty]
        private int _totalOrderQuantity;

        [ObservableProperty]
        private int _completedQuantity;

        [ObservableProperty]
        private int _remainingQuantity;

        [ObservableProperty]
        private double _progressPercentage;

        // 硬件连接状态
        [ObservableProperty]
        private bool _isScanner1Connected; // 质量检测扫码枪

        [ObservableProperty]
        private bool _isScanner2Connected; // 成品拍照扫码枪

        [ObservableProperty]
        private bool _isModbusConnected;

        [ObservableProperty]
        private bool _isScaleConnected;

        [ObservableProperty]
        private bool _isCameraConnected;

        #endregion

        #region 质量检测工序

        [ObservableProperty]
        private string _qualityBarcode = string.Empty;

        [ObservableProperty]
        private bool[] _diStatus = new bool[16]; // 用于存储DI点的状态

        // DI历史记录 - 跟踪每个DI点是否曾经变为True过
        private bool[] _diHistory = new bool[16];

        // DI8监控相关 - 用于停线后自动恢复
        private CancellationTokenSource? _di8MonitorCancellation;
        private bool _isMonitoringDI8 = false;

        // DI9监控和扫码相关字段
        private DateTime _lastDI9TriggerTime = DateTime.MinValue; // 上次DI9触发时间
        private DateTime _lastDO4TriggerTime = DateTime.MinValue; // 上次DO4触发时间
        private bool _isDO4Active = false; // DO4是否正在激活中
        private bool _lastDI9State = false; // 上次DI9状态，用于检测上升沿

        // 插座检测专用字段
        private bool _isSocketDetecting = false; // 是否正在进行插座检测
        private bool _socketDI0InitialState = false; // 插座检测开始时DI0的初始状态
        private bool _lastDI0State = false; // 上次DI0状态
        private bool _lastDI1State = false; // 上次DI1状态

        [ObservableProperty]
        private bool _isQualityPassed;

        [ObservableProperty]
        private string _qualityStatus = "等待扫码";
        [ObservableProperty]
        private string _lastQualityBarcode = string.Empty; // 上一个检测的条码

        [ObservableProperty]
        private bool _lastQualityResult = false; // 上一个检测的结果

        [ObservableProperty]
        private bool _isQualityInspecting = false; // 是否正在进行质量检测

        // 检测历史记录
        [ObservableProperty]
        private ObservableCollection<QualityInspectionHistoryItem> _recentInspections = new();

        #endregion

        #region 成品拍照工序

        [ObservableProperty]
        private string _photoBarcode = string.Empty;

        [ObservableProperty]
        private string _photoFilePath = string.Empty;

        [ObservableProperty]
        private string _photoStatus = "等待扫码";

        [ObservableProperty]
        private string _lastPhotoBarcode = string.Empty; // 上一个拍照的条码

        [ObservableProperty]
        private string _lastPhotoPath = string.Empty; // 上一个拍照的路径

        [ObservableProperty]
        private bool _isPhotoWaiting = false; // 是否正在等待拍照

        [ObservableProperty]
        private string _photoErrorMessage = string.Empty; // 拍照工序错误信息

        [ObservableProperty]
        private bool _hasPhotoError = false; // 是否有拍照工序错误

        [ObservableProperty]
        private bool _isLineStoppedForQuality = false; // 是否因质量问题停线

        // 拍照历史记录
        [ObservableProperty]
        private ObservableCollection<ProductPhotoHistoryItem> _recentPhotos = new();

        // 扫码拍照队列相关
        [ObservableProperty]
        private ObservableCollection<PhotoQueueItem> _photoQueue = new();
        [ObservableProperty]
        private PhotoQueueItem _selectedPhotoSerialBarCode;
        [ObservableProperty]
        private int _queueCount = 0;

        [ObservableProperty]
        private string _currentQueueBarcode = "无"; // 显示队首条码

        [ObservableProperty]
        private string _queueStatus = "等待产品到位"; // 队列状态显示

        [ObservableProperty]
        private int _totalEnqueuedCount = 0; // 总入队数量计数器（用于测试）
        [ObservableProperty]
        private int _totalDI9Count = 0; // 识别到的DI9计数（用于测试）
        [ObservableProperty]
        private int _totalDO4Count = 0; // 识别到的DO4计数（用于测试）
        [ObservableProperty]
        private int _totalBarCodeCount = 0; // 扫到的条码总数（用于测试）

        #endregion

        #region 小盒贴打印工序

        [ObservableProperty]
        private int _boxCount;

        [ObservableProperty]
        private int _boxCapacity; // 从产品信息中获取

        [ObservableProperty]
        private ObservableCollection<string> _currentBoxBarcodes = new();
        [ObservableProperty]
        private ObservableCollection<string> _currentCartonBoxNumbers = new();
        [ObservableProperty]
        private string _boxStatus = "等待产品";

        [ObservableProperty]
        private string _lastBoxNumber = string.Empty; // 上一个打印的盒号

        [ObservableProperty]
        private int _lastBoxBarcodeCount = 0; // 上一个盒子的条码数量

        #endregion

        #region 大箱称重与大箱贴打印工序

        [ObservableProperty]
        private ObservableCollection<string> _cartonQueueBoxNumbers = new(); // 大箱队列中的盒号

        [ObservableProperty]
        private int _cartonQueueCount = 0; // 大箱队列中的盒数

        [ObservableProperty]
        private string _selectedBoxNumber = string.Empty; // 选中的盒号

        [ObservableProperty]
        private decimal _currentWeight = 0.0m; // 当前称重

        [ObservableProperty]
        private string _weightUnit = "kg"; // 重量单位

        [ObservableProperty]
        private string _cartonStatus = "等待装箱"; // 大箱状态

        [ObservableProperty]
        private string _lastCartonNumber = string.Empty; // 上一个打印的箱号

        [ObservableProperty]
        private int _lastCartonBoxCount = 0; // 上一个大箱的盒数

        [ObservableProperty]
        private int _lastCartonTotalQuantity = 0; // 上一个大箱的总产品数量

        [ObservableProperty]
        private bool _canPrintCarton = false; // 是否可以打印大箱贴

        [ObservableProperty]
        private int _cartonCapacity = 2; // 大箱装箱容量（每箱装多少盒）

        // 自动打印大箱贴相关属性
        [ObservableProperty]
        private bool _isAutoPrintingCarton = false; // 是否正在自动打印大箱贴

        [ObservableProperty]
        private string _autoPrintStatus = string.Empty; // 自动打印状态提示

        // 重量监控相关私有字段
        private decimal _previousWeight = 0m; // 上一次的重量
        private DateTime _weightStableStartTime = DateTime.MinValue; // 重量开始稳定的时间
        private bool _isWeightStable = false; // 重量是否稳定
        private bool _isWaitingForWeightZero = false; // 是否等待重量归零
        private CancellationTokenSource? _weightMonitorCts; // 重量监控取消令牌
        private Task? _weightMonitorTask; // 重量监控任务

        // 自动打印配置常量
        private const decimal WeightStabilityThreshold = 0.05m; // 5% 稳定性阈值
        private const int WeightStabilityDelaySeconds = 5; // 稳定等待时间（秒）

        // DI9监控和扫码相关常量
        private const int DO4_TRIGGER_DURATION_MS = 500; // DO4触发持续时间（毫秒）
        private const double SCAN_TIMEOUT_SECONDS = 1.2; // 扫码超时时间（秒）
        private const int DI9_DEBOUNCE_MS = 200; // DI9防抖时间（毫秒）

        #endregion

        #region 状态与日志

        [ObservableProperty]
        private ObservableCollection<string> _statusMessages = new();

        #endregion

        public WorkspaceViewModel(
            IUserService userService,
            IProductService productService,
            IProductionOrderService productionOrderService,
            IMessageService messageService,
            IScaleService scaleService,
            IModbusService modbusService,
            IScannerService scannerService1,
            IScannerService scannerService2,
            ICameraService cameraService,
            IConfigService configService,
            ILogService logService,
            IBarTenderService barTenderService,
            IQualityInspectionRepository qualityInspectionRepository,
            IProductPhotoRepository productPhotoRepository)
        {
            _userService = userService;
            _productService = productService;
            _productionOrderService = productionOrderService;
            _messageService = messageService;
            _scaleService = scaleService;
            _modbusService = modbusService;
            _scannerService1 = scannerService1;
            _scannerService2 = scannerService2;
            _cameraService = cameraService;
            _configService = configService;
            _logService = logService;
            _barTenderService = barTenderService;
            _qualityInspectionRepository = qualityInspectionRepository;
            _productPhotoRepository = productPhotoRepository;

            // 添加硬件连接状态属性
            IsScanner1Connected = false;
            IsScanner2Connected = false;
            IsModbusConnected = false;
            IsScaleConnected = false;
            IsCameraConnected = false;

            // 初始化
            InitializeAsync();
            // 创建定时器用于监控DI点位状态
            var diMonitorTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 100ms轮询一次
            };

            diMonitorTimer.Tick += async (s, e) =>
            {
                // 修复：只要Modbus连接就读取DI状态，不依赖IsQualityInspecting状态
                // 这样即使停线恢复后，DI监控也能正常工作
                if (_modbusService.IsConnected)
                {
                    try
                    {
                        // 读取16个DI状态
                        var inputs = await _modbusService.ReadInputsAsync(0, 16);
                        DiStatus = inputs;

                        // DI9监控：检测产品到位并触发扫码
                        if (inputs.Length > 9)
                        {
                            bool currentDI9State = inputs[9]; // DI9对应数组索引9

                            // 检测DI9从False变为True（上升沿触发）
                            if (!_lastDI9State && currentDI9State)
                            {
                                AddStatusMessage("扫码信号Log：DI9触发了一次");
                                TotalDI9Count++;
                                // 防抖处理
                                var now = DateTime.Now;
                                if ((now - _lastDI9TriggerTime).TotalMilliseconds > DI9_DEBOUNCE_MS)
                                {
                                    _lastDI9TriggerTime = now;

                                    // 处理产品到位事件
                                    await HandleProductArrivedAsync();
                                }
                            }

                            _lastDI9State = currentDI9State;
                        }

                        // 只有在正在检测时才更新DI历史记录和判断结果
                        if (IsQualityInspecting)
                        {
                            // 获取当前产品类别
                            if (SelectedOrder != null)
                            {
                                if (CurrentProduct != null)
                                {
                                    if (CurrentProduct.Category == "插座")
                                    {
                                        // 插座检测：监控DI0和DI1的状态变化
                                        await HandleSocketDetectionAsync(inputs);
                                    }
                                    else
                                    {
                                        // 开关类产品：使用原有的历史记录逻辑
                                        await HandleSwitchDetectionAsync(inputs, CurrentProduct.Category);
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.Error(ex, "读取DI状态失败");
                    }
                }
            };

            diMonitorTimer.Start();
        }

        /// <summary>
        /// 生产模式变化处理
        /// </summary>
        partial void OnSelectedProductionModeChanged(string value)
        {
            IsPackageRepairMode = value == "包装返修";

            if (IsPackageRepairMode)
            {
                // 进入包装返修模式时的校验和处理
                _ = Task.Run(async () => await ValidateAndEnterPackageRepairModeAsync());
            }
            else
            {
                // 切换回正常生产模式
                AddStatusMessage("已切换到正常生产模式");
            }
        }

        /// <summary>
        /// 校验并进入包装返修模式
        /// </summary>
        private async Task ValidateAndEnterPackageRepairModeAsync()
        {
            try
            {
                // 检查当前界面上的各个队列是否还有记录
                bool hasPhotoQueue = PhotoQueue.Count > 0;
                bool hasBoxQueue = CurrentBoxBarcodes.Count > 0;
                bool hasCartonQueue = CurrentCartonBoxNumbers.Count > 0;

                if (hasPhotoQueue || hasBoxQueue || hasCartonQueue)
                {
                    string message = "正常生产还没有结束，不能进行包装返修。\n";
                    if (hasPhotoQueue) message += $"拍照队列中还有 {PhotoQueue.Count} 个条码\n";
                    if (hasBoxQueue) message += $"小盒队列中还有 {CurrentBoxBarcodes.Count} 个条码\n";
                    if (hasCartonQueue) message += $"大箱队列中还有 {CurrentCartonBoxNumbers.Count} 个盒号\n";

                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        _messageService.ShowWarning(message);
                        // 重置回正常生产模式
                        SelectedProductionMode = "正常生产";
                    });
                    return;
                }

                // 成功进入包装返修模式
                AddStatusMessage("✅ 已进入包装返修模式");
                AddStatusMessage("📝 包装返修模式说明：");
                AddStatusMessage("   - 质检扫码枪将忽略所有扫码");
                AddStatusMessage("   - 包装拍照扫码枪正常工作");
                AddStatusMessage("   - 拍照完成后直接显示在历史列表，不进入小盒贴工序");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "进入包装返修模式时发生错误");
                AddStatusMessage($"❌ 进入包装返修模式失败: {ex.Message}");

                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // 重置回正常生产模式
                    SelectedProductionMode = "正常生产";
                });
            }
        }

        private async void InitializeAsync()
        {
            try
            {
                // 从配置文件读取硬件连接参数
                string qualityScannerIp = _configService.GetHardwareSetting("QualityScanner", "IP", "************");
                int qualityScannerPort = _configService.GetHardwareSettingInt("QualityScanner", "Port", 2002);

                string photoScannerIp = _configService.GetHardwareSetting("PhotoScanner", "IP", "************");
                int photoScannerPort = _configService.GetHardwareSettingInt("PhotoScanner", "Port", 2002);

                string cameraIp = _configService.GetHardwareSetting("Camera", "IP", "************");
                int cameraPort = _configService.GetHardwareSettingInt("Camera", "Port", 2003);

                string modbusIp = _configService.GetHardwareSetting("Modbus", "IP", "************");
                int modbusPort = _configService.GetHardwareSettingInt("Modbus", "Port", 502);

                string scalePortName = _configService.GetHardwareSetting("Scale", "PortName", "COM5");
                int scaleBaudRate = _configService.GetHardwareSettingInt("Scale", "BaudRate", 9600);

                // 开始连接硬件设备
                AddStatusMessage("开始连接硬件设备...");

                // 连接质量检测扫码枪
                AddStatusMessage($"正在连接质量检测扫码枪 ({qualityScannerIp}:{qualityScannerPort})...");
                IsScanner1Connected = await _scannerService1.ConnectAsync(qualityScannerIp, qualityScannerPort);
                AddStatusMessage($"质量检测扫码枪连接{(IsScanner1Connected ? "成功" : "失败")}");

                // 连接成品拍照扫码枪
                AddStatusMessage($"正在连接成品拍照扫码枪 ({photoScannerIp}:{photoScannerPort})...");
                IsScanner2Connected = await _scannerService2.ConnectAsync(photoScannerIp, photoScannerPort);
                AddStatusMessage($"成品拍照扫码枪连接{(IsScanner2Connected ? "成功" : "失败")}");

                // 连接相机
                AddStatusMessage($"正在连接相机 ({cameraIp}:{cameraPort})...");
                IsCameraConnected = await _cameraService.ConnectAsync(cameraIp, cameraPort);
                AddStatusMessage($"相机连接{(IsCameraConnected ? "成功" : "失败")}");

                // 连接IO模块
                AddStatusMessage($"正在连接IO模块 ({modbusIp}:{modbusPort})...");
                IsModbusConnected = await _modbusService.ConnectAsync(modbusIp, modbusPort);
                AddStatusMessage($"IO模块连接{(IsModbusConnected ? "成功" : "失败")}");

                // 连接电子秤
                AddStatusMessage($"正在连接电子秤 ({scalePortName})...");
                IsScaleConnected = await _scaleService.ConnectAsync(scalePortName, scaleBaudRate);
                AddStatusMessage($"电子秤连接{(IsScaleConnected ? "成功" : "失败")}");

                // 启动重量监控线程
                if (IsScaleConnected)
                {
                    StartWeightMonitoring();
                }

                // 注册事件处理（先取消已有的事件订阅，避免重复）
                if (IsScanner1Connected && _scannerService1 != null)
                {
                    // 先取消已有的事件订阅
                    _scannerService1.BarcodeReceived -= Scanner1_BarcodeReceived;
                    // 重新订阅事件
                    _scannerService1.BarcodeReceived += Scanner1_BarcodeReceived;
                }

                if (IsScanner2Connected && _scannerService2 != null)
                {
                    // 先取消已有的事件订阅
                    _scannerService2.BarcodeReceived -= Scanner2_BarcodeReceived;
                    // 重新订阅事件
                    _scannerService2.BarcodeReceived += Scanner2_BarcodeReceived;
                }

                if (IsCameraConnected && _cameraService != null)
                {
                    // 先取消已有的事件订阅
                    _cameraService.PhotoCaptured -= Camera_PhotoCaptured;
                    // 重新订阅事件
                    _cameraService.PhotoCaptured += Camera_PhotoCaptured;
                }

                // 启动硬件状态监控定时器
                StartHardwareMonitoring();

                // 注意：不再自动加载历史记录，只有选择订单并确认后才加载对应订单的历史
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "初始化硬件设备失败");
                AddStatusMessage($"初始化硬件设备失败: {ex.Message}");
            }
        }

        // 标记是否正在重新连接硬件，避免重复连接
        private bool _isReconnecting = false;

        private void StartHardwareMonitoring()
        {
            // 创建定时器用于监控硬件状态
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(10) // 每10秒检查一次
            };

            timer.Tick += async (s, e) =>
            {
                try
                {
                    // 如果正在重新连接，则跳过本次检查
                    if (_isReconnecting)
                        return;

                    // 更新硬件连接状态
                    IsScanner1Connected = _scannerService1?.IsConnected ?? false;
                    IsScanner2Connected = _scannerService2?.IsConnected ?? false;
                    IsModbusConnected = _modbusService?.IsConnected ?? false;
                    IsScaleConnected = _scaleService?.IsConnected ?? false;
                    IsCameraConnected = _cameraService?.IsConnected ?? false;

                    // 如果有设备断开，尝试重新连接
                    if (!IsScanner1Connected || !IsScanner2Connected || !IsModbusConnected ||
                        !IsScaleConnected || !IsCameraConnected)
                    {
                        // 设置重连标志
                        _isReconnecting = true;

                        // 重新初始化
                        await Task.Delay(1000); // 延迟1秒后重试
                        await ReconnectDisconnectedHardwareAsync();

                        // 重置重连标志
                        _isReconnecting = false;
                    }

                    // 如果电子秤已连接，定期读取重量
                    if (IsScaleConnected && _scaleService != null)
                    {
                        try
                        {
                            var (weight, unit) = await _scaleService.ReadWeightAndUnitAsync();
                            CurrentWeight = weight;
                            WeightUnit = unit;
                        }
                        catch (Exception ex)
                        {
                            _logService.Error(ex, "读取电子秤数据失败");
                            // 不更新UI，保持上次的值
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logService.Error(ex, "监控硬件状态失败");
                }
            };

            timer.Start();
        }

        /// <summary>
        /// 重新连接断开的硬件设备
        /// </summary>
        private async Task ReconnectDisconnectedHardwareAsync()
        {
            try
            {
                // 从配置文件读取硬件连接参数
                string qualityScannerIp = _configService.GetHardwareSetting("QualityScanner", "IP", "************");
                int qualityScannerPort = _configService.GetHardwareSettingInt("QualityScanner", "Port", 2002);

                string photoScannerIp = _configService.GetHardwareSetting("PhotoScanner", "IP", "************");
                int photoScannerPort = _configService.GetHardwareSettingInt("PhotoScanner", "Port", 2002);

                string cameraIp = _configService.GetHardwareSetting("Camera", "IP", "************");
                int cameraPort = _configService.GetHardwareSettingInt("Camera", "Port", 2003);

                string modbusIp = _configService.GetHardwareSetting("Modbus", "IP", "************");
                int modbusPort = _configService.GetHardwareSettingInt("Modbus", "Port", 502);

                string scalePortName = _configService.GetHardwareSetting("Scale", "PortName", "COM5");
                int scaleBaudRate = _configService.GetHardwareSettingInt("Scale", "BaudRate", 9600);

                // 只重新连接断开的设备
                if (!IsScanner1Connected && _scannerService1 != null)
                {
                    AddStatusMessage($"正在重新连接质量检测扫码枪 ({qualityScannerIp}:{qualityScannerPort})...");
                    IsScanner1Connected = await _scannerService1.ConnectAsync(qualityScannerIp, qualityScannerPort);
                    if (IsScanner1Connected)
                    {
                        AddStatusMessage("质量检测扫码枪重新连接成功");

                        // 重新注册事件处理
                        _scannerService1.BarcodeReceived -= Scanner1_BarcodeReceived;
                        _scannerService1.BarcodeReceived += Scanner1_BarcodeReceived;
                    }
                    else
                    {
                        AddStatusMessage("质量检测扫码枪重新连接失败");
                    }
                }

                if (!IsScanner2Connected && _scannerService2 != null)
                {
                    AddStatusMessage($"正在重新连接成品拍照扫码枪 ({photoScannerIp}:{photoScannerPort})...");
                    IsScanner2Connected = await _scannerService2.ConnectAsync(photoScannerIp, photoScannerPort);
                    if (IsScanner2Connected)
                    {
                        AddStatusMessage("成品拍照扫码枪重新连接成功");

                        // 重新注册事件处理
                        _scannerService2.BarcodeReceived -= Scanner2_BarcodeReceived;
                        _scannerService2.BarcodeReceived += Scanner2_BarcodeReceived;
                    }
                    else
                    {
                        AddStatusMessage("成品拍照扫码枪重新连接失败");
                    }
                }

                if (!IsCameraConnected && _cameraService != null)
                {
                    AddStatusMessage($"正在重新连接相机 ({cameraIp}:{cameraPort})...");
                    IsCameraConnected = await _cameraService.ConnectAsync(cameraIp, cameraPort);
                    if (IsCameraConnected)
                    {
                        AddStatusMessage("相机重新连接成功");

                        // 重新注册事件处理
                        _cameraService.PhotoCaptured -= Camera_PhotoCaptured;
                        _cameraService.PhotoCaptured += Camera_PhotoCaptured;
                    }
                    else
                    {
                        AddStatusMessage("相机重新连接失败");
                    }
                }

                if (!IsModbusConnected && _modbusService != null)
                {
                    AddStatusMessage($"正在重新连接IO模块 ({modbusIp}:{modbusPort})...");
                    IsModbusConnected = await _modbusService.ConnectAsync(modbusIp, modbusPort);
                    if (IsModbusConnected)
                    {
                        AddStatusMessage("IO模块重新连接成功");
                    }
                    else
                    {
                        AddStatusMessage("IO模块重新连接失败");
                    }
                }

                if (!IsScaleConnected && _scaleService != null)
                {
                    AddStatusMessage($"正在重新连接电子秤 ({scalePortName})...");
                    IsScaleConnected = await _scaleService.ConnectAsync(scalePortName, scaleBaudRate);
                    if (IsScaleConnected)
                    {
                        AddStatusMessage("电子秤重新连接成功");
                        StartWeightMonitoring(); // 重新启动重量监控
                    }
                    else
                    {
                        AddStatusMessage("电子秤重新连接失败");
                        StopWeightMonitoring(); // 停止重量监控
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "重新连接硬件设备失败");
                AddStatusMessage($"重新连接硬件设备失败: {ex.Message}");
            }
        }

        #region 插座检测专用方法

        /// <summary>
        /// 处理插座检测逻辑
        /// </summary>
        private async Task HandleSocketDetectionAsync(bool[] inputs)
        {
            if (inputs.Length <= 1) return;

            bool currentDI0 = inputs[0];
            bool currentDI1 = inputs[1];

            // 如果刚开始检测，记录DI0的初始状态
            if (!_isSocketDetecting)
            {
                _isSocketDetecting = true;
                _socketDI0InitialState = currentDI0;
                _lastDI0State = currentDI0;
                _lastDI1State = currentDI1;

                AddStatusMessage($"插座检测开始，DI0初始状态: {(_socketDI0InitialState ? "True" : "False")}");

                // 如果初始状态DI0不是True，说明可能有问题
                if (!_socketDI0InitialState)
                {
                    AddStatusMessage("⚠️ 警告：插座检测开始时DI0不是True状态");
                }
                return;
            }

            // 检测DI0状态变化：从False变为True（检测合格）
            if (!_lastDI0State && currentDI0)
            {
                AddStatusMessage("✅ 插座检测：DI0从False变为True，检测合格");
                await CompleteSocketDetectionAsync(true, "DI0重新变为True，检测合格");
                return;
            }

            // 检测DI1状态变化：从False变为True（检测不合格）
            if (!_lastDI1State && currentDI1)
            {
                AddStatusMessage("❌ 插座检测：DI1变为True，检测不合格");
                await CompleteSocketDetectionAsync(false, "DI1变为True，检测不合格");
                return;
            }

            // 更新状态
            _lastDI0State = currentDI0;
            _lastDI1State = currentDI1;
        }

        /// <summary>
        /// 完成插座检测并保存结果
        /// </summary>
        private async Task CompleteSocketDetectionAsync(bool result, string message)
        {
            try
            {
                // 构造DI状态JSON
                var diData = new
                {
                    CurrentStatus = DiStatus,
                    InitialDI0 = _socketDI0InitialState,
                    FinalDI0 = _lastDI0State,
                    FinalDI1 = _lastDI1State,
                    Result = result,
                    Message = message,
                    Timestamp = DateTime.Now
                };
                string diStatusJson = System.Text.Json.JsonSerializer.Serialize(diData);

                // 更新数据库记录
                await _productionOrderService.UpdateQualityInspectionResultAsync(QualityBarcode, result, diStatusJson);
                AddStatusMessage($"✅ 条码 {QualityBarcode} 检测完成，结果: {(result ? "合格" : "不合格")}");

                // 立即刷新历史列表
                if (SelectedOrder != null)
                {
                    await LoadRecentInspectionsByOrderAsync();
                }
                else
                {
                    await LoadRecentInspectionsAsync();
                }

                // 重置检测状态，等待下一个条码
                LastQualityBarcode = QualityBarcode;
                LastQualityResult = result;
                QualityBarcode = string.Empty;
                IsQualityInspecting = false;
                IsQualityPassed = result;
                QualityStatus = result ? "检测合格" : "检测不合格";

                // 重置插座检测状态
                ResetSocketDetectionState();

                // 等待一段时间后重置状态显示
                _ = Task.Run(async () =>
                {
                    await Task.Delay(3000);
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        QualityStatus = "等待扫码";
                    });
                });
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"完成插座检测时发生错误: {QualityBarcode}");
                AddStatusMessage($"❌ 保存插座检测结果失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置插座检测状态
        /// </summary>
        private void ResetSocketDetectionState()
        {
            _isSocketDetecting = false;
            _socketDI0InitialState = false;
            _lastDI0State = false;
            _lastDI1State = false;
        }

        /// <summary>
        /// 处理开关类产品检测逻辑（原有逻辑）
        /// </summary>
        private async Task HandleSwitchDetectionAsync(bool[] inputs, string productCategory)
        {
            // 更新DI历史记录 - 记录每个DI点是否曾经变为True过
            for (int i = 0; i < inputs.Length && i < _diHistory.Length; i++)
            {
                if (inputs[i])
                {
                    _diHistory[i] = true; // 一旦变为True，就永久记录
                }
            }

            bool previousResult = IsQualityPassed;
            IsQualityPassed = DetermineQualityResultFromHistory(productCategory);
            QualityStatus = IsQualityPassed ? "检测合格" : "检测中...";

            // 如果从不合格变为合格，立即更新数据库记录
            if (!previousResult && IsQualityPassed && !string.IsNullOrEmpty(QualityBarcode))
            {
                // 将DI历史记录转换为JSON字符串
                var diData = new
                {
                    CurrentStatus = DiStatus,
                    History = _diHistory,
                    Timestamp = DateTime.Now
                };
                string diStatusJson = System.Text.Json.JsonSerializer.Serialize(diData);

                await _productionOrderService.UpdateQualityInspectionResultAsync(QualityBarcode, true, diStatusJson);
                AddStatusMessage($"✅ 条码 {QualityBarcode} 检测合格，已立即更新数据库记录");

                // 立即刷新历史列表，显示合格结果
                if (SelectedOrder != null)
                {
                    await LoadRecentInspectionsByOrderAsync();
                }
                else
                {
                    await LoadRecentInspectionsAsync();
                }

                // 保存后重置检测状态，等待下一个条码
                LastQualityBarcode = QualityBarcode;
                LastQualityResult = true;
                QualityBarcode = string.Empty;
                IsQualityInspecting = false;
                QualityStatus = "等待扫码";
            }
        }

        #endregion

        #region DI9监控和扫码拍照队列处理

        /// <summary>
        /// 处理产品到位事件（DI9触发）
        /// </summary>
        private async Task HandleProductArrivedAsync()
        {
            try
            {
                AddStatusMessage("扫码信号Log：📦 检测到产品到位（DI9触发），开始触发扫码");
                QueueStatus = "产品到位，正在触发扫码...";

                // 触发DO4进行扫码
                await TriggerScanAsync();
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "处理产品到位事件失败");
                AddStatusMessage($"❌ 处理产品到位事件失败: {ex.Message}");
                QueueStatus = "处理产品到位失败";
            }
        }

        /// <summary>
        /// 触发DO4进行扫码
        /// </summary>
        private async Task TriggerScanAsync()
        {
            try
            {
                // 如果DO4已经在激活状态，跳过
                if (_isDO4Active)
                {
                    AddStatusMessage("扫码信号Log：⚠️ DO4正在激活中，跳过本次触发");
                    return;
                }

                if (!_modbusService.IsConnected)
                {
                    AddStatusMessage("扫码信号Log：⚠️ IO模块未连接，无法触发扫码");
                    QueueStatus = "IO模块未连接，无法触发扫码";
                    return;
                }

                _isDO4Active = true;
                _lastDO4TriggerTime = DateTime.Now;
                AddStatusMessage("扫码信号Log：🔫 正在触发扫码枪（DO4=True）");
                QueueStatus = "正在触发扫码枪...";

                // 写入DO4=True
                var result = await _modbusService.WriteCoilAsync(4, true);
                AddStatusMessage("扫码信号Log：DO4触发了一次，执行结果为" + result.ToString());
                TotalDO4Count++;

                // 启动扫码超时检测
                _ = Task.Run(async () => await CheckScanTimeoutAsync());

                // 等待指定时间后关闭DO4
                _ = Task.Run(async () =>
                {
                    await Task.Delay(DO4_TRIGGER_DURATION_MS);

                    try
                    {
                        if (_modbusService.IsConnected)
                        {
                            await _modbusService.WriteCoilAsync(4, false);
                            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                AddStatusMessage("扫码信号Log：🔫 扫码触发完成（DO4=False）");
                                QueueStatus = "等待扫码结果...";
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.Error(ex, "关闭DO4失败");
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            AddStatusMessage($"❌ 关闭DO4失败: {ex.Message}");
                        });
                    }
                    finally
                    {
                        _isDO4Active = false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "触发扫码失败");
                AddStatusMessage($"❌ 触发扫码失败: {ex.Message}");
                QueueStatus = "触发扫码失败";
                _isDO4Active = false;
            }
        }

        /// <summary>
        /// 检查扫码超时
        /// </summary>
        private async Task CheckScanTimeoutAsync()
        {
            await Task.Delay(int.Parse((SCAN_TIMEOUT_SECONDS * 1000).ToString()));

            // 检查是否在超时时间内收到了条码
            if (_lastDO4TriggerTime != DateTime.MinValue &&
                (DateTime.Now - _lastDO4TriggerTime).TotalSeconds >= SCAN_TIMEOUT_SECONDS)
            {
                // 超时，触发停线
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    AddStatusMessage($"❌ 扫码超时（{SCAN_TIMEOUT_SECONDS}秒），扫码枪可能故障，触发停线");
                    QueueStatus = "扫码超时，触发停线";

                    // 扫码失败，触发停线
                    await StopLineForQualityAsync();
                });
            }
        }

        /// <summary>
        /// 条码入队
        /// </summary>
        private void EnqueueBarcode(string barcode)
        {
            var queueItem = new PhotoQueueItem
            {
                Barcode = barcode,
                EnqueueTime = DateTime.Now
            };

            PhotoQueue.Add(queueItem);
            QueueCount = PhotoQueue.Count;
            CurrentQueueBarcode = PhotoQueue.FirstOrDefault()?.Barcode ?? "无";

            // 增加总入队计数器
            TotalEnqueuedCount++;

            AddStatusMessage($"📦 条码入队: {barcode} (队列: {QueueCount}, 总计: {TotalEnqueuedCount})");
            QueueStatus = $" {QueueCount} 个产品等待拍照";

            // 条码成功接收，取消超时检测
            _lastDO4TriggerTime = DateTime.MinValue;
        }

        /// <summary>
        /// 拍照完成，出队配对
        /// </summary>
        private async Task ProcessPhotoCompletedAsync(string photoPath)
        {
            if (PhotoQueue.Count == 0)
            {
                AddStatusMessage("⚠️ 队列为空，无法配对照片");
                return;
            }

            var queueItem = PhotoQueue[0]; // 取队首
            PhotoQueue.RemoveAt(0);

            try
            {
                // 保存到数据库
                await SavePhotoResultAsync(queueItem.Barcode, photoPath);

                // 更新历史列表
                await LoadRecentPhotosByOrderAsync();

                // 更新队列状态
                QueueCount = PhotoQueue.Count;
                CurrentQueueBarcode = PhotoQueue.FirstOrDefault()?.Barcode ?? "无";
                QueueStatus = QueueCount > 0 ? $" {QueueCount} 个产品等待拍照" : "队列为空，等待产品到位";

                AddStatusMessage($"📷 拍照完成并配对: {queueItem.Barcode}");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"处理拍照完成失败: {queueItem.Barcode}");
                AddStatusMessage($"❌ 处理拍照完成失败: {ex.Message}");

                // 失败时将条码重新放回队首
                PhotoQueue.Insert(0, queueItem);
                QueueCount = PhotoQueue.Count;
                CurrentQueueBarcode = PhotoQueue.FirstOrDefault()?.Barcode ?? "无";
            }
        }

        #endregion

        #region 硬件事件处理方法

        /// <summary>
        /// 质量检测扫码枪条码接收事件处理
        /// </summary>
        private async void Scanner1_BarcodeReceived(object? sender, string barcode)
        {
            barcode = barcode.Replace("\r", "").Replace("\n", "");

            // 包装返修模式下，质检扫码枪忽略所有扫码
            if (IsPackageRepairMode)
            {
                AddStatusMessage($"包装返修模式：质检扫码枪忽略条码 {barcode}");
                return;
            }

            // 处理新的条码数据
            AddStatusMessage($"质量检测工序收到条码: {barcode}");

            // 验证条码
            bool isValid = await ValidateBarcodeAsync(barcode);
            if (!isValid)
            {
                // 条码无效时，清空条码显示，避免用户困惑
                QualityBarcode = string.Empty;
                QualityStatus = "条码无效";
                IsQualityInspecting = false;

                // 刷新历史列表，显示最新的检测记录（可能包含刚才扫的已合格条码）
                await LoadRecentInspectionsByOrderAsync();
                return;
            }

            // 条码有效，设置当前条码
            QualityBarcode = barcode;

            // 先刷新历史列表（避免显示即将创建的不合格记录）
            await LoadRecentInspectionsByOrderAsync();

            // 立即保存不合格记录（新方案：每个条码扫描后立即有数据库记录）
            // 注意：这里不刷新UI，避免显示临时的不合格记录
            await SaveQualityInspectionResultAsync(barcode, false, refreshUI: false);
            AddStatusMessage($"✅ 已为条码 {barcode} 创建初始检测记录（不合格状态）");

            // 获取产品类别，根据类别进行不同的初始化
            if (SelectedOrder != null)
            {
                var product = await _productService.GetByCodeAsync(SelectedOrder.ProductCode);
                if (product != null)
                {
                    AddStatusMessage($"产品类别: {product.Category}，开始检测...");

                    if (product.Category == "插座")
                    {
                        // 插座检测：重置插座检测状态
                        ResetSocketDetectionState();
                        QualityStatus = "插座检测中，等待DI状态变化...";
                    }
                    else
                    {
                        // 开关类产品：重置DI历史记录
                        ResetDiHistory();
                        QualityStatus = "正在检测...";
                    }
                }
            }
            else
            {
                // 没有选择订单时，使用默认逻辑
                ResetDiHistory();
                QualityStatus = "正在检测...";
            }

            // 开始检测
            IsQualityInspecting = true;
            IsQualityPassed = false; // 初始状态为不合格
        }

        /// <summary>
        /// 成品拍照扫码枪条码接收事件处理（新队列模式）
        /// </summary>
        private async void Scanner2_BarcodeReceived(object? sender, string barcode)
        {
            barcode = barcode.Replace("\r", "").Replace("\n", "");
            HasPhotoError = false;
            PhotoErrorMessage = string.Empty;
            AddStatusMessage($"扫码信号Log：收到扫码枪条码: {barcode}");
            TotalBarCodeCount++;

            // 验证条码
            bool isValid = await ValidatePhotoBarcodeAsync(barcode);
            if (!isValid)
            {
                return; // 错误信息已在ValidatePhotoBarcodeAsync中设置
            }

            // 条码有效，加入队列
            EnqueueBarcode(barcode);
        }

        /// <summary>
        /// 相机拍照完成事件处理（新队列模式）
        /// </summary>
        private async void Camera_PhotoCaptured(object? sender, string photoFilePath)
        {
            // 过滤单字符错误返回（如相机识别出错返回"\"）
            if (string.IsNullOrWhiteSpace(photoFilePath) || photoFilePath.Length <= 10)
            {
                AddStatusMessage($"⚠️ 相机返回无效路径（仅{photoFilePath?.Length ?? 0}个字符），已忽略: '{photoFilePath}'");
                return;
            }

            AddStatusMessage($"相机拍照完成，路径: {photoFilePath}");

            // 处理拍照完成，与队首条码配对
            await ProcessPhotoCompletedAsync(photoFilePath);
        }

        #endregion

        [RelayCommand]
        private async Task LoginAsync()
        {
            if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
            {
                _messageService.ShowWarning("请输入用户名和密码");
                return;
            }

            try
            {
                bool isValid = await _userService.ValidatePasswordAsync(Username, Password);
                if (isValid)
                {
                    CurrentUser = await _userService.GetByUsernameAsync(Username);
                    IsLoggedIn = true;
                    AddStatusMessage($"用户 {CurrentUser?.Name ?? Username} 已登录");

                    // 登录成功后加载可用订单
                    await LoadAvailableOrdersAsync();
                }
                else
                {
                    _messageService.ShowError("用户名或密码错误");
                }
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"登录失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void Logout()
        {
            CurrentUser = null;
            IsLoggedIn = false;
            Username = string.Empty;
            Password = string.Empty;
            AddStatusMessage("用户已登出");
        }

        private async Task LoadAvailableOrdersAsync()
        {
            try
            {
                // 加载未完成的订单
                var orders = await _productionOrderService.GetUncompletedOrdersAsync();
                AvailableOrders.Clear();
                foreach (var order in orders)
                {
                    AvailableOrders.Add(order);
                }
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"加载订单失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task SelectOrderAsync()
        {
            if (SelectedOrder == null)
            {
                _messageService.ShowWarning("请选择一个订单");
                return;
            }

            try
            {
                // 校验：如果界面上的队列中有条码，则不允许换单
                bool hasPhotoQueue = PhotoQueue.Count > 0;
                bool hasBoxQueue = CurrentBoxBarcodes.Count > 0;
                bool hasCartonQueue = CurrentCartonBoxNumbers.Count > 0;

                if (hasPhotoQueue || hasBoxQueue || hasCartonQueue)
                {
                    string message = "当前有产品在生产中，不允许换单。\n";
                    if (hasPhotoQueue) message += $"成品拍照工序队列中有 {PhotoQueue.Count} 个条码\n";
                    if (hasBoxQueue) message += $"小盒贴队列中有 {CurrentBoxBarcodes.Count} 个条码\n";
                    if (hasCartonQueue) message += $"大箱称重队列中有 {CurrentCartonBoxNumbers.Count} 个小盒码\n";
                    message += "请等待当前产品完成生产后再换单。";

                    _messageService.ShowWarning(message);
                    return;
                }

                // 加载产品信息
                CurrentProduct = await _productService.GetByCodeAsync(SelectedOrder.ProductCode);
                if (CurrentProduct == null)
                {
                    _messageService.ShowError("无法加载产品信息");
                    return;
                }

                // 设置装箱数量
                BoxCapacity = CurrentProduct.BoxQuantity;

                // 计算订单进度
                TotalOrderQuantity = SelectedOrder.Quantity;
                CompletedQuantity = await _productionOrderService.GetCompletedQuantityAsync(SelectedOrder.Id);
                RemainingQuantity = TotalOrderQuantity - CompletedQuantity;
                ProgressPercentage = (double)CompletedQuantity / TotalOrderQuantity * 100;

                // 加载小盒队列
                await LoadBoxQueueAsync();

                // 加载大箱队列
                await LoadCartonQueueAsync();

                // 加载该订单的检测历史记录
                await LoadRecentInspectionsByOrderAsync();

                // 加载该订单的拍照历史记录
                await LoadRecentPhotosByOrderAsync();

                AddStatusMessage($"已选择订单: {SelectedOrder.OrderNumber}, 产品: {CurrentProduct.Name}");
                AddStatusMessage($"已加载订单 {SelectedOrder.OrderNumber} 的历史记录");
            }
            catch (Exception ex)
            {
                _messageService.ShowError($"选择订单失败: {ex.Message}");
            }
        }
        [RelayCommand]
        private async Task SubmitQualityResultAsync()
        {
            if (string.IsNullOrEmpty(QualityBarcode))
            {
                AddStatusMessage("没有条码，无法提交质量检测结果");
                return;
            }

            // 检查是否已经保存过合格结果
            var existingQualityStatus = await _productionOrderService.GetBarcodeQualityStatusAsync(QualityBarcode);
            if (existingQualityStatus == true)
            {
                AddStatusMessage($"条码 {QualityBarcode} 已保存合格结果，无需重复提交");
            }
            else if (IsQualityPassed)
            {
                // 如果当前检测结果是合格，更新数据库记录
                var diData = new
                {
                    CurrentStatus = DiStatus,
                    History = _diHistory,
                    Timestamp = DateTime.Now
                };
                string diStatusJson = System.Text.Json.JsonSerializer.Serialize(diData);

                await _productionOrderService.UpdateQualityInspectionResultAsync(QualityBarcode, true, diStatusJson);
                AddStatusMessage($"条码 {QualityBarcode} 的质量检测结果已提交: 合格");

                // 刷新历史列表，显示合格结果
                if (SelectedOrder != null)
                {
                    await LoadRecentInspectionsByOrderAsync();
                }
                else
                {
                    await LoadRecentInspectionsAsync();
                }
            }
            else
            {
                // 如果当前检测结果是不合格，数据库中已经有不合格记录，无需更新
                AddStatusMessage($"条码 {QualityBarcode} 的质量检测结果已确认: 不合格");

                // 刷新历史列表，显示最终的不合格结果
                if (SelectedOrder != null)
                {
                    await LoadRecentInspectionsByOrderAsync();
                }
                else
                {
                    await LoadRecentInspectionsAsync();
                }
            }

            // 重置状态
            LastQualityBarcode = QualityBarcode;
            LastQualityResult = IsQualityPassed;
            QualityBarcode = string.Empty;
            IsQualityInspecting = false;
            QualityStatus = "等待扫码";
            IsQualityPassed = false;

            // 重置DI历史记录，准备下一次检测
            ResetDiHistory();
        }
        [RelayCommand]
        private void RemoveSelectedItem()
        {
            if (SelectedPhotoSerialBarCode == null) return;
            string barCode = SelectedPhotoSerialBarCode.Barcode.ToLower();
            for (int i = PhotoQueue.Count - 1; i >= 0; i--)
            {
                if (PhotoQueue[i].Barcode.ToLower() == barCode)
                {
                    PhotoQueue.RemoveAt(i);
                }
            }
            QueueCount = PhotoQueue.Count;
            QueueStatus = QueueCount > 0 ? $" {QueueCount} 个产品等待拍照" : "队列为空，等待产品到位";
        }

        /// <summary>
        /// 验证条码是否属于当前订单（质量检测工序）
        /// </summary>
        private async Task<bool> ValidateBarcodeAsync(string barcode)
        {
            if (SelectedOrder == null)
            {
                AddStatusMessage("未选择订单，无法验证条码");
                return false;
            }

            try
            {
                // 检查条码是否属于当前订单
                var barcodeEntity = await _productionOrderService.GetBarcodeAsync(barcode);
                if (barcodeEntity == null || barcodeEntity.OrderId != SelectedOrder.Id)
                {
                    AddStatusMessage($"条码 {barcode} 不属于当前订单");
                    return false;
                }

                // 检查条码的质量检测状态
                var qualityStatus = await _productionOrderService.GetBarcodeQualityStatusAsync(barcode);
                if (qualityStatus.HasValue)
                {
                    if (qualityStatus.Value)
                    {
                        // 已检测且合格 - 不允许重复检测
                        AddStatusMessage($"条码 {barcode} 已完成质量检测且合格，无需重复检测");
                        return false;
                    }
                    else
                    {
                        // 已检测但不合格 - 允许重新检测（返工重检）
                        AddStatusMessage($"条码 {barcode} 之前检测不合格，允许重新检测");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"验证条码 {barcode} 时发生错误");
                AddStatusMessage($"验证条码时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证条码是否属于当前订单且有合格的质量检测记录（成品拍照工序）
        /// </summary>
        private async Task<bool> ValidatePhotoBarcodeAsync(string barcode)
        {
            if (SelectedOrder == null)
            {
                AddStatusMessage("未选择订单，无法验证条码");
                return false;
            }

            try
            {
                // 检查条码是否属于当前订单
                var barcodeEntity = await _productionOrderService.GetBarcodeAsync(barcode);
                if (barcodeEntity == null || barcodeEntity.OrderId != SelectedOrder.Id)
                {
                    PhotoStatus = "❌ 条码无效";
                    PhotoErrorMessage = $"条码 {barcode} 不属于当前订单";
                    HasPhotoError = true;
                    AddStatusMessage($"❌ 条码 {barcode} 不属于当前订单");
                    StartPhotoErrorClearTimer();
                    return false;
                }

                // 检查条码的最新质量检测记录
                var latestQualityResult = await _productionOrderService.GetBarcodeQualityStatusAsync(barcode);
                if (latestQualityResult == null)
                {
                    // 没有质量检测记录，属于漏检，需要停线
                    PhotoStatus = "❌ 漏检产品，请取走产品";
                    PhotoErrorMessage = $"条码 {barcode} 漏检，请取走产品";
                    HasPhotoError = true;
                    AddStatusMessage($"⚠️ 条码 {barcode} 漏检，线体已自动停止");

                    // 保留条码显示，但设置为不等待拍照状态
                    IsPhotoWaiting = false;

                    // 停止线体
                    await StopLineForQualityAsync();

                    // 不启动自动清除定时器，需要手动恢复
                    return false;
                }
                else if (latestQualityResult == false)
                {
                    // 最新质量检测结果不合格，停止线体
                    PhotoStatus = "❌ 产品不合格，请取走产品";
                    PhotoErrorMessage = $"条码 {barcode} 质检不合格，请取走产品";
                    HasPhotoError = true;
                    AddStatusMessage($"⚠️ 条码 {barcode} 质量检测不合格，线体已自动停止");

                    // 保留条码显示，但设置为不等待拍照状态
                    IsPhotoWaiting = false;

                    // 停止线体
                    await StopLineForQualityAsync();

                    // 不启动自动清除定时器，需要手动恢复
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"验证拍照条码 {barcode} 时发生错误");
                AddStatusMessage($"验证条码时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重置DI历史记录
        /// </summary>
        private void ResetDiHistory()
        {
            for (int i = 0; i < _diHistory.Length; i++)
            {
                _diHistory[i] = false;
            }
            AddStatusMessage("已重置DI历史记录，开始新的检测周期");
        }

        /// <summary>
        /// 根据产品类别和DI历史记录判断质量检测结果
        /// </summary>
        private bool DetermineQualityResultFromHistory(string productCategory)
        {
            switch (productCategory)
            {
                case "一路开关":
                    // 一路开关需要判断第0、1点位都曾经为True过
                    bool oneWayResult = _diHistory[0] && _diHistory[1];
                    if (oneWayResult)
                    {
                        AddStatusMessage("一路开关检测：DI0-1都已触发，检测合格");
                    }
                    return oneWayResult;

                case "二路开关":
                    // 二路开关需要判断第0、1、2、3点位都曾经为True过
                    bool twoWayResult = _diHistory[0] && _diHistory[1] && _diHistory[2] && _diHistory[3];
                    if (twoWayResult)
                    {
                        AddStatusMessage("二路开关检测：DI0-3都已触发，检测合格");
                    }
                    return twoWayResult;

                case "三路开关":
                    // 三路开关测试逻辑：只要DI0为true过就判定为合格
                    bool threeWayResult = _diHistory[0];
                    if (threeWayResult)
                    {
                        AddStatusMessage("三路开关检测：DI0已触发，检测合格（测试模式）");
                    }
                    else
                    {
                        AddStatusMessage("三路开关检测：DI0未触发，检测不合格");
                    }
                    return threeWayResult;

                case "四路开关":
                    // 四路开关需要判断第0、1、2、3、4、5、6、7点位都曾经为True过
                    bool fourWayResult = _diHistory[0] && _diHistory[1] && _diHistory[2] && _diHistory[3] &&
                                        _diHistory[4] && _diHistory[5] && _diHistory[6] && _diHistory[7];
                    if (fourWayResult)
                    {
                        AddStatusMessage("四路开关检测：DI0-7都已触发，检测合格");
                    }
                    return fourWayResult;

                case "插座":
                    // 插座检测：只要DI0触发过就合格（与三路开关保持一致）
                    bool socketResult = _diHistory[0];
                    if (socketResult)
                    {
                        AddStatusMessage("插座检测：DI0已触发，检测合格");
                    }
                    return socketResult;

                default:
                    AddStatusMessage($"未知产品类别: {productCategory}，无法判断质量检测结果");
                    return false;
            }
        }

        /// <summary>
        /// 根据产品类别判断质量检测结果（旧方法，保留兼容性）
        /// </summary>
        private bool DetermineQualityResult(string productCategory)
        {
            // 现在使用历史记录方法
            return DetermineQualityResultFromHistory(productCategory);
        }

        /// <summary>
        /// 保存质量检测结果
        /// </summary>
        private async Task SaveQualityInspectionResultAsync(string barcode, bool result, bool refreshUI = true)
        {
            if (SelectedOrder == null || CurrentUser == null)
            {
                AddStatusMessage("未选择订单或未登录，无法保存质量检测结果");
                return;
            }

            try
            {
                // 获取产品信息
                var product = await _productService.GetByCodeAsync(SelectedOrder.ProductCode);
                if (product == null)
                {
                    AddStatusMessage($"找不到产品信息: {SelectedOrder.ProductCode}");
                    return;
                }

                // 将DI历史记录转换为JSON字符串（保存的是整个检测过程中哪些DI点曾经触发过）
                var diData = new
                {
                    CurrentStatus = DiStatus,  // 当前DI状态
                    History = _diHistory,      // DI历史记录（哪些点曾经触发过）
                    Timestamp = DateTime.Now
                };
                string diStatusJson = System.Text.Json.JsonSerializer.Serialize(diData);

                // 保存质量检测记录
                await _productionOrderService.SaveQualityInspectionAsync(
                    barcode,
                    SelectedOrder.Id,
                    product.Id,
                    product.Category,
                    result,
                    diStatusJson,
                    CurrentUser.Id,
                    string.Empty
                );

                AddStatusMessage($"已保存条码 {barcode} 的质量检测结果: {(result ? "合格" : "不合格")}");

                // 只有在需要刷新UI时才刷新检测历史记录
                if (refreshUI)
                {
                    if (SelectedOrder != null)
                    {
                        await LoadRecentInspectionsByOrderAsync();
                    }
                    else
                    {
                        await LoadRecentInspectionsAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"保存质量检测结果时发生错误: {barcode}");
                AddStatusMessage($"保存质量检测结果时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载最近的检测历史记录（所有订单）
        /// </summary>
        private async Task LoadRecentInspectionsAsync()
        {
            try
            {
                var inspections = await _qualityInspectionRepository.GetRecentInspectionsWithOrderInfoAsync(100);

                RecentInspections.Clear();
                foreach (var inspection in inspections)
                {
                    RecentInspections.Add(new QualityInspectionHistoryItem
                    {
                        OrderNumber = inspection.OrderNumber,
                        ProductSpecification = inspection.ProductSpecification,
                        ProductCategory = inspection.ProductCategory,
                        Barcode = inspection.Barcode,
                        InspectionTime = inspection.CreatedAt,
                        Result = inspection.Result,
                        OperatorId = inspection.OperatorId
                    });
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "加载检测历史记录失败");
            }
        }

        /// <summary>
        /// 加载指定订单的检测历史记录
        /// </summary>
        private async Task LoadRecentInspectionsByOrderAsync()
        {
            if (SelectedOrder == null) return;

            try
            {
                var inspections = await _qualityInspectionRepository.GetRecentInspectionsByOrderIdAsync(SelectedOrder.Id, 100);

                RecentInspections.Clear();
                foreach (var inspection in inspections)
                {
                    RecentInspections.Add(new QualityInspectionHistoryItem
                    {
                        OrderNumber = inspection.OrderNumber,
                        ProductSpecification = inspection.ProductSpecification,
                        ProductCategory = inspection.ProductCategory,
                        Barcode = inspection.Barcode,
                        InspectionTime = inspection.CreatedAt,
                        Result = inspection.Result,
                        OperatorId = inspection.OperatorId
                    });
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "加载订单检测历史记录失败");
            }
        }

        /// <summary>
        /// 加载最近的拍照历史记录（所有订单）
        /// </summary>
        private async Task LoadRecentPhotosAsync()
        {
            try
            {
                var photos = await _productPhotoRepository.GetRecentPhotosWithOrderInfoAsync(100);

                RecentPhotos.Clear();
                foreach (var photo in photos)
                {
                    RecentPhotos.Add(new ProductPhotoHistoryItem
                    {
                        OrderNumber = photo.OrderNumber,
                        ProductSpecification = photo.ProductSpecification,
                        ProductCategory = photo.ProductCategory,
                        Barcode = photo.Barcode,
                        PhotoTime = photo.CreatedAt,
                        PhotoPath = photo.PhotoPath,
                        OperatorId = photo.OperatorId
                    });
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "加载拍照历史记录失败");
            }
        }

        /// <summary>
        /// 加载指定订单的拍照历史记录
        /// </summary>
        private async Task LoadRecentPhotosByOrderAsync()
        {
            if (SelectedOrder == null) return;

            try
            {
                var photos = await _productPhotoRepository.GetRecentPhotosByOrderIdAsync(SelectedOrder.Id, 100);

                RecentPhotos.Clear();
                foreach (var photo in photos)
                {
                    RecentPhotos.Add(new ProductPhotoHistoryItem
                    {
                        OrderNumber = photo.OrderNumber,
                        ProductSpecification = photo.ProductSpecification,
                        ProductCategory = photo.ProductCategory,
                        Barcode = photo.Barcode,
                        PhotoTime = photo.CreatedAt,
                        PhotoPath = photo.PhotoPath,
                        OperatorId = photo.OperatorId
                    });
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "加载订单拍照历史记录失败");
            }
        }

        /// <summary>
        /// 查看照片命令
        /// </summary>
        [RelayCommand]
        private void ViewPhoto(string photoPath)
        {
            try
            {
                photoPath = photoPath.Replace("\r", "").Replace("\n", "");
                if (string.IsNullOrEmpty(photoPath) || !System.IO.File.Exists(photoPath))
                {
                    _messageService.ShowWarning("照片文件不存在或路径无效");
                    return;
                }

                // 使用默认程序打开照片
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = photoPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"打开照片失败: {photoPath}");
                _messageService.ShowError($"打开照片失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存成品拍照结果
        /// </summary>
        private async Task SavePhotoResultAsync(string barcode, string photoPath)
        {
            if (SelectedOrder == null || CurrentUser == null)
            {
                AddStatusMessage("未选择订单或未登录，无法保存拍照结果");
                return;
            }

            try
            {
                AddStatusMessage($"🔄 开始保存条码 {barcode} 的拍照结果...");
                _logService.Info($"开始保存拍照结果: 条码={barcode}, 照片路径={photoPath}");

                // 检查是否有照片
                if (string.IsNullOrEmpty(photoPath))
                {
                    AddStatusMessage($"❌ 条码 {barcode} 没有拍照，跳过保存，不计入下一工序");
                    _logService.Info($"条码 {barcode} 没有照片路径，跳过保存");
                    return;
                }

                // 再次验证条码的质量检测状态，确保只有合格产品才能进入下一工序
                var qualityStatus = await _productionOrderService.GetBarcodeQualityStatusAsync(barcode);
                if (qualityStatus == null)
                {
                    AddStatusMessage($"❌ 条码 {barcode} 没有质量检测记录，不能进入下一工序");
                    _logService.Warn($"条码 {barcode} 没有质量检测记录，跳过保存");
                    return;
                }
                else if (qualityStatus == false)
                {
                    AddStatusMessage($"❌ 条码 {barcode} 质量检测不合格，不能进入下一工序");
                    _logService.Warn($"条码 {barcode} 质量检测不合格，跳过保存");
                    return;
                }

                // 保存成品拍照记录
                _logService.Info($"调用SaveProductPhotoAsync: 条码={barcode}, 订单ID={SelectedOrder.Id}, 用户ID={CurrentUser.Id}");
                await _productionOrderService.SaveProductPhotoAsync(
                    barcode,
                    SelectedOrder.Id,
                    photoPath,
                    CurrentUser.Id
                );

                AddStatusMessage($"✅ 已保存条码 {barcode} 的拍照信息，照片路径: {photoPath}");
                _logService.Info($"成功保存拍照结果: 条码={barcode}");

                // 保存成功后刷新拍照历史记录
                await LoadRecentPhotosByOrderAsync();

                // 包装返修模式下，拍照完成后不进入小盒贴工序
                if (IsPackageRepairMode)
                {
                    AddStatusMessage($"📦 包装返修模式：条码 {barcode} 拍照完成，流程结束，不进入小盒贴工序");
                }
                else
                {
                    // 正常生产模式：只有质量检测合格的产品才加入小盒贴打印队列
                    await AddBarcodeToBoxQueueAsync(barcode);
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"保存拍照结果时发生错误: {barcode}");
                AddStatusMessage($"❌ 保存拍照结果时发生错误: {ex.Message}");
                throw; // 重新抛出异常，让调用方知道保存失败
            }
        }

        [RelayCommand]
        private async Task SubmitPhotoResultAsync()
        {
            if (string.IsNullOrEmpty(PhotoBarcode))
            {
                AddStatusMessage("❌ 没有条码，无法提交拍照结果");
                return;
            }

            try
            {
                AddStatusMessage($"📤 正在提交条码 {PhotoBarcode} 的拍照结果...");
                _logService.Info($"用户手动提交拍照结果: 条码={PhotoBarcode}, 照片路径={PhotoFilePath}");

                await SavePhotoResultAsync(PhotoBarcode, PhotoFilePath);

                // 重置状态
                LastPhotoBarcode = PhotoBarcode;
                LastPhotoPath = PhotoFilePath;
                PhotoBarcode = string.Empty;
                IsPhotoWaiting = false;
                PhotoStatus = "等待扫码";
                PhotoFilePath = string.Empty;

                AddStatusMessage($"✅ 条码 {LastPhotoBarcode} 的拍照结果已成功提交");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"提交拍照结果失败: {PhotoBarcode}");
                AddStatusMessage($"❌ 提交拍照结果失败: {ex.Message}");
                // 生产环境中不弹对话框，只在状态栏显示错误信息
            }
        }

        /// <summary>
        /// 清空拍照队列命令
        /// </summary>
        [RelayCommand]
        private void ClearPhotoQueue()
        {
            if (PhotoQueue.Count == 0)
            {
                AddStatusMessage("队列已为空");
                return;
            }

            int count = PhotoQueue.Count;
            PhotoQueue.Clear();
            QueueCount = 0;
            CurrentQueueBarcode = "无";
            QueueStatus = "队列已清空，等待产品到位";

            AddStatusMessage($"🗑️ 已清空拍照队列，移除了 {count} 个条码");
        }

        /// <summary>
        /// 移除队首条码命令（跳过异常产品）
        /// </summary>
        [RelayCommand]
        private void RemoveFirstQueueItem()
        {
            if (PhotoQueue.Count == 0)
            {
                AddStatusMessage("队列为空，无法移除");
                return;
            }

            var removedItem = PhotoQueue[0];
            PhotoQueue.RemoveAt(0);
            QueueCount = PhotoQueue.Count;
            CurrentQueueBarcode = PhotoQueue.FirstOrDefault()?.Barcode ?? "无";
            QueueStatus = QueueCount > 0 ? $" {QueueCount} 个产品等待拍照" : "队列为空，等待产品到位";

            AddStatusMessage($"🗑️ 已移除队首条码: {removedItem.Barcode}");
        }

        /// <summary>
        /// 测试停线状态命令（仅用于调试）
        /// </summary>
        [RelayCommand]
        private async Task TestStopLineAsync()
        {
            await StopLineForQualityAsync();
            AddStatusMessage("🔧 测试：手动触发停线状态");
        }

        /// <summary>
        /// 重置总入队计数器命令（仅用于测试）
        /// </summary>
        [RelayCommand]
        private void ResetTotalEnqueuedCount()
        {
            TotalEnqueuedCount = 0;
            AddStatusMessage("🔄 总入队计数器已重置");
        }

        /// <summary>
        /// 恢复线体运行命令（仅恢复物理线体）
        /// </summary>
        [RelayCommand]
        private async Task RestoreLineAsync()
        {
            try
            {
                // 显示确认对话框
                var result = _messageService.ShowQuestion("请确认已将不合格产品从线体上取走，恢复线体运行？");
                if (result != true) return; // 用户取消操作

                // 停止DI8监控
                StopDI8Monitoring();

                // 执行恢复线体的实际操作
                await RestoreLineInternalAsync();
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "恢复线体运行失败");
                AddStatusMessage($"❌ 恢复线体运行失败: {ex.Message}");
                // 即使出现异常，也要清除拍照工序的错误状态
                PhotoStatus = "等待扫码";
                HasPhotoError = false;
                PhotoErrorMessage = string.Empty;
            }
        }

        /// <summary>
        /// 停止线体运行（仅控制物理线体，不影响软件检测功能）
        /// </summary>
        private async Task StopLineForQualityAsync()
        {
            try
            {
                if (_modbusService.IsConnected)
                {
                    // 停止线体运行：DO0写入True
                    //await _modbusService.WriteCoilAsync(0, true);
                    await _modbusService.WriteRegisterAsync(0, 256);
                    AddStatusMessage("⚠️ 检测到不合格产品，线体已自动停止");
                }
                else
                {
                    AddStatusMessage("⚠️ 检测到不合格产品，线体应停止（IO模块未连接，无法控制实际线体）");
                }

                // 启动DI8监控，等待用户取走不合格产品
                StartDI8Monitoring();

                // 注意：不设置IsLineStoppedForQuality = true，让软件功能继续正常工作
                // 只是物理停线，软件的质量检测功能应该继续可用
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "停止线体运行失败");
                AddStatusMessage($"❌ 停止线体运行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动拍照错误状态自动清除定时器
        /// </summary>
        private void StartPhotoErrorClearTimer()
        {
            // 8秒后自动清除错误状态
            _ = Task.Delay(8000).ContinueWith(_ =>
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    if (HasPhotoError)
                    {
                        HasPhotoError = false;
                        PhotoErrorMessage = string.Empty;
                        if (PhotoStatus.StartsWith("❌"))
                        {
                            PhotoStatus = "等待扫码";
                        }
                    }
                });
            });
        }

        /// <summary>
        /// 加载小盒队列
        /// </summary>
        private async Task LoadBoxQueueAsync()
        {
            try
            {
                if (SelectedOrder == null)
                {
                    CurrentBoxBarcodes.Clear();
                    BoxCount = 0;
                    PrintBoxLabelCommand.NotifyCanExecuteChanged();
                    return;
                }

                // 从数据库加载队列
                var queueBarcodes = await _productionOrderService.GetBoxQueueBarcodesAsync(SelectedOrder.Id);

                // 在 UI 线程上更新集合
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    CurrentBoxBarcodes.Clear();
                    foreach (var barcode in queueBarcodes)
                    {
                        CurrentBoxBarcodes.Add(barcode);
                    }

                    BoxCount = CurrentBoxBarcodes.Count;
                });

                if (BoxCount > 0)
                {
                    BoxStatus = $"队列中有 {BoxCount}/{BoxCapacity} 个条码";
                }
                else
                {
                    BoxStatus = "等待产品";
                }

                // 通知命令状态变化
                PrintBoxLabelCommand.NotifyCanExecuteChanged();
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "加载小盒队列失败");
                AddStatusMessage($"加载小盒队列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将条码加入小盒贴打印队列
        /// </summary>
        private async Task AddBarcodeToBoxQueueAsync(string barcode)
        {
            try
            {
                if (SelectedOrder == null || CurrentProduct == null)
                {
                    AddStatusMessage("未选择订单或产品，无法添加条码到队列");
                    return;
                }

                //验证条码是否是返修品，如果是返修品，则不往装箱队列中添加，而给用户提示装到哪个盒中
                var repairBoxNumber = await _productionOrderService.GetBoxNumberByBarCode(barcode);
                if (!string.IsNullOrEmpty(repairBoxNumber))
                {
                    PhotoErrorMessage = $"{barcode}是返修品，请自行装入{repairBoxNumber}号包装盒";
                    HasPhotoError = true;
                    return;
                }
                // 添加到数据库队列
                await _productionOrderService.AddBarcodeToBoxQueueAsync(barcode, SelectedOrder.Id, CurrentProduct.Id);

                // 重新加载队列
                await LoadBoxQueueAsync();

                AddStatusMessage($"条码 {barcode} 已加入小盒贴打印队列，当前队列: {BoxCount}/{BoxCapacity}");

                // 检查是否达到装箱数量
                if (BoxCount >= BoxCapacity)
                {
                    await PrintBoxLabelAsync();
                }
                else
                {
                    BoxStatus = $"等待装盒 ({BoxCount}/{BoxCapacity})";
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"添加条码到小盒贴队列失败: {barcode}");
                AddStatusMessage($"添加条码到队列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否可以打印小盒贴
        /// </summary>
        private bool CanPrintBoxLabel()
        {
            return SelectedOrder != null &&
                   CurrentUser != null &&
                   CurrentProduct != null &&
                   CurrentBoxBarcodes.Count > 0;
        }

        /// <summary>
        /// 打印小盒贴
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanPrintBoxLabel))]
        private async Task PrintBoxLabelAsync()
        {
            if (SelectedOrder == null || CurrentUser == null || CurrentProduct == null)
            {
                AddStatusMessage("缺少必要信息，无法打印小盒贴");
                return;
            }

            if (CurrentBoxBarcodes.Count == 0)
            {
                AddStatusMessage("队列中没有条码，无法打印小盒贴");
                return;
            }

            try
            {
                BoxStatus = "正在打印小盒贴...";

                // ⚠️ 关键修复：在开始打印时就确定要打印的条码列表，避免打印过程中新加入的条码被误包含
                // 只取前BoxCapacity个条码进行打印
                var barcodesToPrint = CurrentBoxBarcodes.Take(BoxCapacity).ToList();

                AddStatusMessage($"开始打印小盒贴，确定打印条码数量: {barcodesToPrint.Count}");

                // 生成盒号
                string boxNumber = await _productionOrderService.GenerateBoxNumberAsync();

                // 准备打印参数
                var printParams = new Dictionary<string, string>
                {
                    ["产品名称"] = CurrentProduct.Name,
                    ["产品型号"] = CurrentProduct.Specification ?? string.Empty,
                    ["电气参数"] = CurrentProduct.ElectricalParameters ?? string.Empty,
                    ["包装数量"] = barcodesToPrint.Count.ToString(), // 使用实际打印的数量
                    ["69码"] = CurrentProduct.EanCode ?? string.Empty,
                    ["盒号"] = boxNumber
                };

                // 调用BarTender打印
                bool printSuccess = await _barTenderService.PrintBoxLabelAsync(printParams);
                if (!printSuccess)
                {
                    AddStatusMessage("❌ 小盒贴打印失败，但数据已保存");
                    BoxStatus = "打印失败";
                    return;
                }

                // 保存盒装记录（使用确定的条码列表）
                await _productionOrderService.SaveBoxPackageAsync(
                    boxNumber,
                    SelectedOrder.Id,
                    CurrentProduct.Id,
                    barcodesToPrint,
                    CurrentUser.Id
                );

                // 从数据库队列中删除已打印的条码（只删除实际打印的条码）
                await _productionOrderService.RemoveBarcodesFromBoxQueueAsync(barcodesToPrint);

                // 更新状态
                LastBoxNumber = boxNumber;
                LastBoxBarcodeCount = barcodesToPrint.Count;

                AddStatusMessage($"✅ 小盒贴打印完成！盒号: {boxNumber}，包含 {barcodesToPrint.Count} 个条码");
                AddStatusMessage($"条码列表: {string.Join(", ", barcodesToPrint)}");

                // 将盒号加入大箱队列
                await AddBoxToCartonQueueAsync(boxNumber);

                // 重新加载队列，更新界面显示
                await LoadBoxQueueAsync();

                // 更新生产进度
                await UpdateProductionProgressAsync();
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "打印小盒贴失败");
                AddStatusMessage($"打印小盒贴失败: {ex.Message}");
                BoxStatus = "打印失败";
            }
        }

        [RelayCommand]
        private async Task ReprintLastBoxLabelAsync()
        {
            if (string.IsNullOrEmpty(LastBoxNumber) || CurrentProduct == null)
            {
                AddStatusMessage("没有可重新打印的盒贴");
                return;
            }

            try
            {
                // 准备打印参数
                var printParams = new Dictionary<string, string>
                {
                    ["产品名称"] = CurrentProduct.Name,
                    ["产品型号"] = CurrentProduct.Specification ?? string.Empty,
                    ["电气参数"] = CurrentProduct.ElectricalParameters ?? string.Empty,
                    ["包装数量"] = CurrentProduct.BoxQuantity.ToString(),
                    ["69码"] = CurrentProduct.EanCode ?? string.Empty,
                    ["盒号"] = LastBoxNumber
                };

                // 调用BarTender重新打印
                bool printSuccess = await _barTenderService.PrintBoxLabelAsync(printParams);
                if (printSuccess)
                {
                    AddStatusMessage($"✅ 重新打印盒贴完成！盒号: {LastBoxNumber}");
                }
                else
                {
                    AddStatusMessage($"❌ 重新打印盒贴失败！盒号: {LastBoxNumber}");
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"重新打印盒贴失败: {LastBoxNumber}");
                AddStatusMessage($"重新打印盒贴失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载大箱队列
        /// </summary>
        private async Task LoadCartonQueueAsync()
        {
            try
            {
                if (SelectedOrder == null)
                {
                    CartonQueueBoxNumbers.Clear();
                    CartonQueueCount = 0;
                    UpdateCartonStatus();
                    return;
                }

                // 从数据库加载大箱队列中的盒号
                var queueBoxNumbers = await _productionOrderService.GetCartonQueueBoxNumbersAsync(SelectedOrder.Id);

                // 在 UI 线程上更新集合
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    CartonQueueBoxNumbers.Clear();
                    foreach (var boxNumber in queueBoxNumbers)
                    {
                        CartonQueueBoxNumbers.Add(boxNumber);
                    }

                    // 更新计数
                    CartonQueueCount = CartonQueueBoxNumbers.Count;
                });

                // 更新大箱状态
                UpdateCartonStatus();

                if (CartonQueueCount > 0)
                {
                    AddStatusMessage($"加载大箱队列完成，当前队列: {CartonQueueCount} 个盒号");
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "加载大箱队列失败");
                AddStatusMessage($"加载大箱队列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将盒号加入大箱队列
        /// </summary>
        private async Task AddBoxToCartonQueueAsync(string boxNumber)
        {
            try
            {
                if (SelectedOrder == null || CurrentProduct == null)
                {
                    AddStatusMessage("缺少订单或产品信息，无法添加到大箱队列");
                    return;
                }

                // 添加到数据库
                await _productionOrderService.AddBoxToCartonQueueAsync(boxNumber, SelectedOrder.Id, CurrentProduct.Id);

                // 重新加载大箱队列以更新界面
                await LoadCartonQueueAsync();

                AddStatusMessage($"盒号 {boxNumber} 已加入大箱队列，当前队列: {CartonQueueCount}/{CartonCapacity}");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"添加盒号到大箱队列失败: {boxNumber}");
                AddStatusMessage($"添加盒号到大箱队列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新大箱状态
        /// </summary>
        private void UpdateCartonStatus()
        {
            int queueCount = CartonQueueCount;
            if (queueCount == 0)
            {
                CartonStatus = "等待小盒";
                CanPrintCarton = false;
            }
            else if (queueCount < CartonCapacity)
            {
                CartonStatus = $"等待装箱 ({queueCount}/{CartonCapacity})";
                CanPrintCarton = queueCount > 0; // 允许不足容量时也打印
            }
            else
            {
                CartonStatus = $"可以装箱 ({queueCount}/{CartonCapacity})";
                CanPrintCarton = true;
            }
        }

        /// <summary>
        /// 打印大箱贴
        /// </summary>
        [RelayCommand]
        private async Task PrintCartonLabelAsync()
        {
            if (SelectedOrder == null || CurrentUser == null || CurrentProduct == null)
            {
                AddStatusMessage("缺少必要信息，无法打印大箱贴");
                return;
            }

            if (CartonQueueCount == 0)
            {
                AddStatusMessage("大箱队列为空，无法打印大箱贴");
                return;
            }

            // 验证重量
            if (CurrentWeight < 1.0m)
            {
                AddStatusMessage("❌ 重量异常小于1kg，请检查电子秤，无法执行打印");
                //return;
            }

            try
            {
                CartonStatus = "正在打印大箱贴...";

                // 取出要装箱的盒号（最多CartonCapacity个）
                var boxesToPack = await _productionOrderService.GetBoxNumbersForCartonPackingAsync(SelectedOrder.Id, CartonCapacity);

                // 生成箱号
                string cartonNumber = await _productionOrderService.GenerateCartonNumberAsync();

                // 获取所有条码
                var allBarcodes = await GetBarcodesByBoxNumbersAsync(boxesToPack);

                // 准备基础打印参数
                var baseParameters = new Dictionary<string, string>
                {
                    ["产品型号"] = CurrentProduct.Specification ?? string.Empty,
                    ["数量"] = allBarcodes.Count.ToString(),
                    ["采购订单号"] = SelectedOrder.OrderNumber,
                    ["生产日期"] = DateTime.Today.ToString("yyyy年MM月dd日"),
                    ["箱号"] = cartonNumber,
                    ["重量"] = CurrentWeight.ToString()
                };

                // 分页打印大箱贴
                bool printSuccess = await _barTenderService.PrintCartonLabelWithBarcodesAsync(allBarcodes, baseParameters);
                if (!printSuccess)
                {
                    AddStatusMessage("❌ 大箱贴打印失败，但数据已保存");
                    CartonStatus = "打印失败";
                    return;
                }

                // 保存大箱记录
                bool saveSuccess = await _productionOrderService.SaveCartonPackageAsync(
                    cartonNumber,
                    SelectedOrder.Id,
                    CurrentProduct.Id,
                    boxesToPack,
                    CurrentWeight,
                    CurrentUser.Id
                );

                if (saveSuccess)
                {
                    // 更新状态
                    LastCartonNumber = cartonNumber;
                    LastCartonBoxCount = boxesToPack.Count;
                    LastCartonTotalQuantity = allBarcodes.Count;

                    AddStatusMessage($"✅ 大箱贴打印完成！箱号: {cartonNumber}");
                    AddStatusMessage($"包含 {boxesToPack.Count} 个小盒，共 {allBarcodes.Count} 个产品，重量: {CurrentWeight}kg");
                    AddStatusMessage($"盒号列表: {string.Join(", ", boxesToPack)}");

                    // 从数据库队列中移除已装箱的盒号
                    await _productionOrderService.RemoveBoxNumbersFromCartonQueueAsync(boxesToPack);

                    // 重新加载大箱队列以更新界面
                    await LoadCartonQueueAsync();
                }
                else
                {
                    AddStatusMessage("❌ 保存大箱记录失败");
                    CartonStatus = "保存失败";
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "打印大箱贴失败");
                AddStatusMessage($"打印大箱贴失败: {ex.Message}");
                CartonStatus = "打印失败";
            }
        }

        /// <summary>
        /// 根据盒号列表获取所有条码
        /// </summary>
        private async Task<List<string>> GetBarcodesByBoxNumbersAsync(List<string> boxNumbers)
        {
            var allBarcodes = new List<string>();

            try
            {
                foreach (var boxNumber in boxNumbers)
                {
                    // 这里需要实现根据盒号获取条码的逻辑
                    // 暂时使用模拟数据
                    var barcodes = await GetBarcodesByBoxNumberAsync(boxNumber);
                    allBarcodes.AddRange(barcodes);
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "获取条码列表失败");
            }

            return allBarcodes;
        }

        /// <summary>
        /// 根据盒号获取条码列表
        /// </summary>
        private async Task<List<string>> GetBarcodesByBoxNumberAsync(string boxNumber)
        {
            try
            {
                return await _productionOrderService.GetBarcodesByBoxNumberAsync(boxNumber);
            }
            catch (Exception ex)
            {
                _logService.Error(ex, $"获取盒号 {boxNumber} 的条码列表失败");
                return new List<string>();
            }
        }

        /// <summary>
        /// 更新生产进度
        /// </summary>
        private async Task UpdateProductionProgressAsync()
        {
            try
            {
                if (SelectedOrder == null) return;
                // 重新计算完成数量
                CompletedQuantity = await _productionOrderService.GetCompletedQuantityAsync(SelectedOrder.Id);
                RemainingQuantity = TotalOrderQuantity - CompletedQuantity;
                ProgressPercentage = TotalOrderQuantity > 0 ? (double)CompletedQuantity / TotalOrderQuantity * 100 : 0;

                AddStatusMessage($"生产进度更新: {CompletedQuantity}/{TotalOrderQuantity} ({ProgressPercentage:F1}%)");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "更新生产进度失败");
                AddStatusMessage($"更新生产进度失败: {ex.Message}");
            }
        }

        private void AddStatusMessage(string message)
        {
            string timestampedMessage = $"[{DateTime.Now:HH:mm:ss}] {message}";

            // 添加到界面显示
            StatusMessages.Insert(0, timestampedMessage);
            if (StatusMessages.Count > 100) // 限制消息数量
            {
                StatusMessages.RemoveAt(StatusMessages.Count - 1);
            }

            // 同时写入日志文件
            _logService.Info(message);
        }

        #region DI8监控相关方法

        /// <summary>
        /// 启动DI8监控，等待用户取走不合格产品
        /// </summary>
        private void StartDI8Monitoring()
        {
            // 如果已经在监控，先停止
            StopDI8Monitoring();

            if (!_modbusService.IsConnected)
            {
                AddStatusMessage("⚠️ IO模块未连接，无法启动DI8监控");
                return;
            }

            _isMonitoringDI8 = true;
            _di8MonitorCancellation = new CancellationTokenSource();

            AddStatusMessage("🔍 已启动DI8监控，等待用户取走不合格产品...");

            // 启动监控任务
            _ = Task.Run(async () => await MonitorDI8Async(_di8MonitorCancellation.Token));
        }

        /// <summary>
        /// 停止DI8监控
        /// </summary>
        private void StopDI8Monitoring()
        {
            if (_isMonitoringDI8)
            {
                _isMonitoringDI8 = false;
                _di8MonitorCancellation?.Cancel();
                _di8MonitorCancellation?.Dispose();
                _di8MonitorCancellation = null;
                AddStatusMessage("🔍 DI8监控已停止");
            }
        }

        /// <summary>
        /// DI8监控任务
        /// </summary>
        private async Task MonitorDI8Async(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && _modbusService.IsConnected)
                {
                    try
                    {
                        // 读取DI状态
                        var inputs = await _modbusService.ReadInputsAsync(0, 16);

                        // 添加调试信息：显示DI8的当前状态
                        if (inputs.Length > 8)
                        {
                            bool di8Status = inputs[8]; // DI8对应数组索引8（从0开始计数）

                            // 每5秒输出一次DI8状态（避免日志过多）
                            if (DateTime.Now.Second % 5 == 0 && DateTime.Now.Millisecond < 200)
                            {
                                // 在UI线程上添加状态消息
                                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                                {
                                    AddStatusMessage($"🔍 DI8监控中... DI8当前状态: {(di8Status ? "True" : "False")}");
                                });
                            }

                            // 检查DI8是否为true
                            if (di8Status)
                            {
                                // 在UI线程上执行恢复操作和添加状态消息
                                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                                {
                                    AddStatusMessage("✅ 检测到DI8触发，用户已取走不合格产品，自动恢复线体运行");
                                    await RestoreLineInternalAsync();
                                });

                                // 停止监控
                                break;
                            }
                        }
                        else
                        {
                            // 在UI线程上添加错误消息
                            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                AddStatusMessage($"⚠️ DI读取异常：只读取到 {inputs.Length} 个DI点，需要至少9个");
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logService.Error(ex, "DI8监控读取失败");
                        // 继续监控，不中断
                    }

                    // 等待100ms后再次检查
                    await Task.Delay(100, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不记录错误
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "DI8监控任务异常");
            }
            finally
            {
                _isMonitoringDI8 = false;
            }
        }

        /// <summary>
        /// 恢复线体运行的内部实现（不显示确认对话框）
        /// </summary>
        private async Task RestoreLineInternalAsync()
        {
            try
            {
                // 停止DI8监控（如果正在监控）
                StopDI8Monitoring();

                // 清除拍照工序的错误状态
                PhotoStatus = "等待扫码";
                HasPhotoError = false;
                PhotoErrorMessage = string.Empty;

                if (_modbusService.IsConnected)
                {
                    // 恢复线体运行：DO0写入False
                    //await _modbusService.WriteCoilAsync(0, false);
                    await _modbusService.WriteRegisterAsync(0, 0);
                    AddStatusMessage("✅ 线体已恢复运行");
                }
                else
                {
                    AddStatusMessage("✅ 拍照工序错误状态已清除（IO模块未连接，无法控制实际线体）");
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "恢复线体运行失败");
                AddStatusMessage($"❌ 恢复线体运行失败: {ex.Message}");
                // 即使出现异常，也要清除拍照工序的错误状态
                PhotoStatus = "等待扫码";
                HasPhotoError = false;
                PhotoErrorMessage = string.Empty;
            }
        }

        #endregion

        #region 重量监控和自动打印大箱贴

        /// <summary>
        /// 启动重量监控线程
        /// </summary>
        private void StartWeightMonitoring()
        {
            // 如果已经在监控，先停止
            StopWeightMonitoring();

            if (!IsScaleConnected || _scaleService == null)
            {
                AddStatusMessage("⚠️ 电子秤未连接，无法启动重量监控");
                return;
            }

            _weightMonitorCts = new CancellationTokenSource();
            _weightMonitorTask = Task.Run(async () => await WeightMonitoringLoopAsync(_weightMonitorCts.Token));

            AddStatusMessage("🔍 重量监控已启动，等待自动打印大箱贴...");
        }

        /// <summary>
        /// 停止重量监控线程
        /// </summary>
        private void StopWeightMonitoring()
        {
            try
            {
                _weightMonitorCts?.Cancel();
                _weightMonitorCts?.Dispose();
                _weightMonitorCts = null;

                // 等待任务完成（最多等待1秒）
                if (_weightMonitorTask != null && !_weightMonitorTask.IsCompleted)
                {
                    _weightMonitorTask.Wait(1000);
                }
                _weightMonitorTask?.Dispose();
                _weightMonitorTask = null;

                // 重置状态
                _previousWeight = 0m;
                _isWeightStable = false;
                _isWaitingForWeightZero = false;
                _weightStableStartTime = DateTime.MinValue;

                AddStatusMessage("🔍 重量监控已停止");
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "停止重量监控时发生错误");
            }
        }

        /// <summary>
        /// 重量监控循环
        /// </summary>
        private async Task WeightMonitoringLoopAsync(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && IsScaleConnected)
                {
                    try
                    {
                        // 在UI线程上处理重量变化（使用界面显示的CurrentWeight）
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            ProcessWeightChange();
                        });
                    }
                    catch (Exception ex)
                    {
                        _logService.Error(ex, "重量监控处理失败");
                        // 继续监控，不中断
                    }

                    // 每500ms检查一次
                    await Task.Delay(500, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不记录错误
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "重量监控任务异常");
            }
        }

        /// <summary>
        /// 处理重量变化
        /// </summary>
        private void ProcessWeightChange()
        {
            try
            {
                // 使用界面显示的当前重量，更加稳妥可靠
                decimal currentWeight = CurrentWeight;

                // 如果正在自动打印，跳过处理
                if (IsAutoPrintingCarton)
                {
                    return;
                }

                // 如果等待重量归零
                if (_isWaitingForWeightZero)
                {
                    if (currentWeight <= 0.1m) // 重量接近0
                    {
                        _isWaitingForWeightZero = false;
                        _previousWeight = 0m;
                        _isWeightStable = false;
                        _weightStableStartTime = DateTime.MinValue;
                        AutoPrintStatus = "等待下次称重...";
                        AddStatusMessage("📦 重量已归零，开始新的监控周期");
                    }
                    return;
                }

                // 检测重量从0变为正数
                if (_previousWeight <= 0.1m && currentWeight > 0.1m)
                {
                    // 开始新的监控周期
                    _isWeightStable = false;
                    _weightStableStartTime = DateTime.Now;
                    _previousWeight = currentWeight;
                    AutoPrintStatus = "检测到重量变化，开始稳定性监控...";
                    AddStatusMessage($"📦 检测到重量变化: {_previousWeight:F1}kg → {currentWeight:F1}kg，开始稳定性监控");
                    return;
                }

                // 如果重量已经是正数，检查稳定性
                if (_previousWeight > 0.1m && currentWeight > 0.1m)
                {
                    // 计算重量变化百分比
                    decimal changePercentage = Math.Abs(currentWeight - _previousWeight) / _previousWeight;

                    if (changePercentage <= WeightStabilityThreshold) // 5%以内的变化算稳定
                    {
                        if (!_isWeightStable)
                        {
                            // 刚开始稳定
                            _isWeightStable = true;
                            _weightStableStartTime = DateTime.Now;
                            AutoPrintStatus = $"重量稳定中... ({WeightStabilityDelaySeconds}秒倒计时)";
                        }
                        else
                        {
                            // 检查是否已经稳定足够长时间
                            var stableDuration = DateTime.Now - _weightStableStartTime;
                            var remainingSeconds = WeightStabilityDelaySeconds - (int)stableDuration.TotalSeconds;

                            if (stableDuration.TotalSeconds >= WeightStabilityDelaySeconds)
                            {
                                // 重量已稳定足够时间，检查是否可以自动打印
                                _ = Task.Run(async () => await TryAutoPrintCartonAsync());
                            }
                            else if (remainingSeconds > 0)
                            {
                                AutoPrintStatus = $"重量稳定中... ({remainingSeconds}秒)";
                            }
                        }
                    }
                    else
                    {
                        // 重量变化超过阈值，重新开始稳定性检测
                        _isWeightStable = false;
                        _weightStableStartTime = DateTime.Now;
                        AutoPrintStatus = "重量变化中，重新开始稳定性监控...";
                    }

                    _previousWeight = currentWeight;
                }
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "处理重量变化时发生错误");
            }
        }

        /// <summary>
        /// 尝试自动打印大箱贴
        /// </summary>
        private async Task TryAutoPrintCartonAsync()
        {
            try
            {
                // 在UI线程上检查条件和执行打印
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    // 检查是否有待打印的盒号
                    if (CartonQueueCount == 0)
                    {
                        AutoPrintStatus = "重量稳定，但大箱队列为空";
                        AddStatusMessage("📦 重量稳定，但大箱队列为空，无法自动打印");
                        return;
                    }

                    // 检查基本条件
                    if (SelectedOrder == null || CurrentUser == null || CurrentProduct == null)
                    {
                        AutoPrintStatus = "重量稳定，但缺少必要信息";
                        AddStatusMessage("📦 重量稳定，但缺少订单/用户/产品信息，无法自动打印");
                        return;
                    }

                    // 开始自动打印
                    IsAutoPrintingCarton = true;
                    AutoPrintStatus = "正在自动打印大箱贴...";
                    AddStatusMessage($"📦 重量稳定({CurrentWeight:F1}kg)，队列有{CartonQueueCount}个盒号，开始自动打印大箱贴");

                    try
                    {
                        // 调用打印大箱贴的方法
                        await PrintCartonLabelAsync();

                        // 打印完成，等待重量归零
                        _isWaitingForWeightZero = true;
                        AutoPrintStatus = "打印完成，等待取走货物...";
                        AddStatusMessage("📦 自动打印大箱贴完成，等待取走货物");
                    }
                    catch (Exception ex)
                    {
                        _logService.Error(ex, "自动打印大箱贴失败");
                        AutoPrintStatus = "自动打印失败";
                        AddStatusMessage($"📦 自动打印大箱贴失败: {ex.Message}");
                    }
                    finally
                    {
                        IsAutoPrintingCarton = false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logService.Error(ex, "尝试自动打印大箱贴时发生错误");
            }
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            // 停止DI8监控
            StopDI8Monitoring();

            // 停止重量监控
            StopWeightMonitoring();

            // 重置插座检测状态
            ResetSocketDetectionState();
        }

        #endregion
    }
}