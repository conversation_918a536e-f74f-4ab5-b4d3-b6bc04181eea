@echo off
chcp 65001 >nul
echo ================================
echo   硬件设备模拟器启动脚本
echo ================================
echo.
echo 模拟器包含以下硬件设备：
echo - 质量检测扫码枪 (TCP端口: 2002)
echo - 成品拍照扫码枪 (TCP端口: 2003)
echo - 相机模拟器 (TCP端口: 2004)
echo - IO模块/Modbus (TCP端口: 502)
echo - 电子秤模拟器 (串口: COM5)
echo.

echo 正在编译项目...
dotnet build

if %ERRORLEVEL% EQU 0 (
    echo 编译成功，启动模拟器...
    echo.
    dotnet run
) else (
    echo 编译失败，请检查错误信息
    pause
)
