using OutletProductionPacking.Core.DTOs;
using OutletProductionPacking.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    /// <summary>
    /// 生产明细查询服务接口
    /// </summary>
    public interface IProductionDetailQueryService
    {
        /// <summary>
        /// 获取订单汇总列表
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <returns>订单汇总列表</returns>
        Task<List<OrderSummaryDto>> GetOrderSummaryAsync(ProductionDetailQueryParams queryParams);

        /// <summary>
        /// 获取订单汇总总数
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <returns>总数</returns>
        Task<int> GetOrderSummaryCountAsync(ProductionDetailQueryParams queryParams);

        /// <summary>
        /// 获取条码明细列表
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <returns>条码明细列表</returns>
        Task<List<BarcodeDetailDto>> GetBarcodeDetailsAsync(BarcodeDetailQueryParams queryParams);

        /// <summary>
        /// 获取条码明细总数
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <returns>总数</returns>
        Task<int> GetBarcodeDetailsCountAsync(BarcodeDetailQueryParams queryParams);

        /// <summary>
        /// 获取所有操作员列表（用于下拉选择）
        /// </summary>
        /// <returns>操作员列表</returns>
        Task<List<User>> GetOperatorsAsync();

        /// <summary>
        /// 获取所有产品编码列表（用于下拉选择）
        /// </summary>
        /// <returns>产品编码列表</returns>
        Task<List<string>> GetProductCodesAsync();

        /// <summary>
        /// 导出订单汇总到Excel
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        Task<bool> ExportOrderSummaryToExcelAsync(ProductionDetailQueryParams queryParams, string filePath);

        /// <summary>
        /// 导出条码明细到Excel
        /// </summary>
        /// <param name="queryParams">查询参数</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        Task<bool> ExportBarcodeDetailsToExcelAsync(BarcodeDetailQueryParams queryParams, string filePath);

        /// <summary>
        /// 导出完整报表到Excel（包含汇总和明细）
        /// </summary>
        /// <param name="orderQueryParams">订单查询参数</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        Task<bool> ExportCompleteReportToExcelAsync(ProductionDetailQueryParams orderQueryParams, string filePath);
    }
}
