using System.Collections.Generic;
using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    public interface IBarTenderService
    {
        /// <summary>
        /// 打印标签
        /// </summary>
        /// <param name="templatePath">模板文件路径</param>
        /// <param name="parameters">打印参数</param>
        /// <param name="printerName">打印机名称</param>
        /// <param name="copies">打印份数</param>
        /// <returns>是否打印成功</returns>
        Task<bool> PrintLabelAsync(string templatePath, Dictionary<string, string> parameters, string printerName = "", int copies = 1);

        /// <summary>
        /// 打印小盒贴
        /// </summary>
        /// <param name="parameters">打印参数</param>
        /// <param name="copies">打印份数</param>
        /// <returns>是否打印成功</returns>
        Task<bool> PrintBoxLabelAsync(Dictionary<string, string> parameters, int copies = 1);

        /// <summary>
        /// 打印大箱贴
        /// </summary>
        /// <param name="parameters">打印参数</param>
        /// <param name="copies">打印份数</param>
        /// <returns>是否打印成功</returns>
        Task<bool> PrintCartonLabelAsync(Dictionary<string, string> parameters, int copies = 1);

        /// <summary>
        /// 分页打印大箱贴（每页最多20个条码）
        /// </summary>
        /// <param name="barcodes">所有条码列表</param>
        /// <param name="otherParameters">其他打印参数</param>
        /// <returns>是否全部打印成功</returns>
        Task<bool> PrintCartonLabelWithBarcodesAsync(List<string> barcodes, Dictionary<string, string> otherParameters);
    }
}
