using System;
using System.Collections.Generic;

namespace OutletProductionPacking.Data.Models
{
    public class ProductionOrder
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string? ProductSpecification { get; set; }
        public int Quantity { get; set; }
        public DateTime CreatedAt { get; set; }
        public bool IsFinished { get; set; } = false; // 默认为未完成

        // Navigation property
        public virtual ICollection<ProductionOrderBarcode> Barcodes { get; set; }
    }
}
