===============================================
透明COM5电子秤模拟器使用说明
===============================================

目标：让您的软件以为连接的是真实电子秤，无需修改任何代码

原理：
- 使用com0com创建虚拟串口对：COM5 ↔ COM99
- 您的软件连接COM5（以为是真实电子秤）
- 模拟器连接COM99（模拟电子秤行为）
- 数据在COM5和COM99之间透明传输

===============================================
设置步骤
===============================================

步骤1：创建虚拟串口对
-------------------------------
1. 以管理员身份运行：com0com_setup.bat
2. 确认看到：
   CNCA2 PortName=COM5
   CNCB2 PortName=COM99

步骤2：启动电子秤模拟器
-------------------------------
1. 运行 OutletProductionPacking.MockDevices.exe
2. 点击"启动电子秤"按钮
3. 确认状态显示：运行中 (端口:COM99)

步骤3：连接您的软件
-------------------------------
1. 启动您的主程序
2. 连接COM5（就像连接真实电子秤一样）
3. 应该能正常连接，无任何错误

===============================================
使用方法
===============================================

设置重量：
- 在模拟器界面输入重量值
- 点击"设置"按钮
- 重量数据会立即发送到COM5
- 您的软件会收到新的重量数据

快速设置：
- 点击预设按钮：1.0kg, 2.5kg, 5.0kg, 10.0kg
- 重量会立即更新并发送

模拟重量波动：
- 模拟器会自动产生小幅重量波动
- 模拟真实电子秤的行为

===============================================
工作原理
===============================================

数据流向：
模拟器(COM99) ←→ com0com虚拟串口对 ←→ 您的软件(COM5)

通信协议：
- 支持Modbus RTU协议
- 自动响应重量查询命令
- 主动发送重量变化数据

===============================================
故障排除
===============================================

问题1：您的软件连接COM5失败
解决：
1. 确认com0com虚拟串口对已创建
2. 确认模拟器已启动并连接COM99
3. 重启电脑让虚拟串口生效

问题2：收不到重量数据
解决：
1. 在模拟器中点击"设置"按钮
2. 检查模拟器日志是否显示"已主动发送重量数据"
3. 确认您的软件正在监听串口数据

问题3：模拟器连接COM99失败
解决：
1. 重新运行com0com_setup.bat
2. 检查设备管理器中的虚拟串口
3. 重启电脑

===============================================
验证方法
===============================================

1. 运行check_com5.bat检查COM5状态
2. 在设备管理器中查看：
   - com0com - serial port emulator (COM5)
   - com0com - serial port emulator (COM99)
3. 模拟器日志显示连接成功
4. 您的软件能正常连接COM5

===============================================
优势
===============================================

✓ 完全透明：您的软件无需任何修改
✓ 真实模拟：支持标准Modbus RTU协议
✓ 即时响应：设置重量后立即发送数据
✓ 稳定可靠：使用成熟的com0com技术
✓ 易于调试：可以看到所有通信日志

===============================================
注意事项
===============================================

1. 必须以管理员权限运行com0com_setup.bat
2. 模拟器必须先于您的软件启动
3. 如果更换电脑，需要重新安装com0com
4. 虚拟串口对创建后，重启电脑效果更好

===============================================
