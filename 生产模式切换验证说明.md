# 生产模式切换验证说明

## 📋 功能概述

实现了生产模式切换时的队列验证机制，确保模式切换的安全性和数据完整性。

## 🔄 模式切换验证逻辑

### 1. 正常生产 → 包装返修模式

#### 验证条件
- **拍照队列**：必须为空
- **小盒队列**：必须为空  
- **大箱队列**：必须为空

#### 验证逻辑
```csharp
/// <summary>
/// 校验并进入包装返修模式
/// </summary>
private async Task ValidateAndEnterPackageRepairModeAsync()
{
    try
    {
        // 检查当前界面上的各个队列是否还有记录
        bool hasPhotoQueue = PhotoQueue.Count > 0;
        bool hasBoxQueue = CurrentBoxBarcodes.Count > 0;
        bool hasCartonQueue = CurrentCartonBoxNumbers.Count > 0;

        if (hasPhotoQueue || hasBoxQueue || hasCartonQueue)
        {
            string message = "正常生产还没有结束，不能进行包装返修。\n";
            if (hasPhotoQueue) message += $"拍照队列中还有 {PhotoQueue.Count} 个条码\n";
            if (hasBoxQueue) message += $"小盒队列中还有 {CurrentBoxBarcodes.Count} 个条码\n";
            if (hasCartonQueue) message += $"大箱队列中还有 {CurrentCartonBoxNumbers.Count} 个盒号\n";

            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                _messageService.ShowWarning(message);
                // 重置回正常生产模式
                SelectedProductionMode = "正常生产";
            });
            return;
        }

        // 成功进入包装返修模式
        AddStatusMessage("✅ 已进入包装返修模式");
        AddStatusMessage("📝 包装返修模式说明：");
        AddStatusMessage("   - 质检扫码枪将忽略所有扫码");
        AddStatusMessage("   - 包装拍照扫码枪正常工作");
        AddStatusMessage("   - 拍照完成后直接显示在历史列表，不进入小盒贴工序");
    }
    catch (Exception ex)
    {
        _logService.Error(ex, "进入包装返修模式时发生错误");
        AddStatusMessage($"❌ 进入包装返修模式失败: {ex.Message}");

        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
        {
            // 重置回正常生产模式
            SelectedProductionMode = "正常生产";
        });
    }
}
```

#### 失败处理
- 显示警告提示，说明哪些队列还有数据
- 自动回退到正常生产模式
- 记录错误日志

### 2. 包装返修 → 正常生产模式

#### 验证条件
- **拍照队列**：必须为空

#### 验证逻辑
```csharp
/// <summary>
/// 校验并进入正常生产模式
/// </summary>
private async Task ValidateAndEnterNormalModeAsync()
{
    try
    {
        // 检查拍照队列是否还有记录
        bool hasPhotoQueue = PhotoQueue.Count > 0;

        if (hasPhotoQueue)
        {
            string message = "包装返修还没有结束，不能切换到正常生产模式。\n";
            message += $"拍照队列中还有 {PhotoQueue.Count} 个条码\n";
            message += "请等待拍照队列清空后再切换到正常生产模式。";

            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                _messageService.ShowWarning(message);
                // 重置回包装返修模式
                SelectedProductionMode = "包装返修";
            });
            return;
        }

        // 成功进入正常生产模式
        AddStatusMessage("✅ 已切换到正常生产模式");
        AddStatusMessage("📝 正常生产模式说明：");
        AddStatusMessage("   - 质检扫码枪正常工作");
        AddStatusMessage("   - 包装拍照扫码枪正常工作");
        AddStatusMessage("   - 拍照完成后进入小盒贴工序");
        AddStatusMessage("   - 严格验证条码重复和质量状态");
    }
    catch (Exception ex)
    {
        _logService.Error(ex, "切换到正常生产模式时发生错误");
        AddStatusMessage($"❌ 切换到正常生产模式失败: {ex.Message}");

        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
        {
            // 重置回包装返修模式
            SelectedProductionMode = "包装返修";
        });
    }
}
```

#### 失败处理
- 显示警告提示，说明拍照队列还有数据
- 自动回退到包装返修模式
- 记录错误日志

## 🎯 验证目的

### 1. 数据完整性保护
- 确保正在处理的产品不会因为模式切换而丢失
- 防止队列中的数据状态不一致

### 2. 流程安全性
- 避免在生产过程中意外切换模式
- 确保每个模式下的业务逻辑正确执行

### 3. 用户体验
- 提供清晰的错误提示
- 自动回退到安全状态
- 避免用户困惑

## 📊 验证场景

### 场景1：正常切换
```
正常生产模式（所有队列为空） → 包装返修模式 ✅
包装返修模式（拍照队列为空） → 正常生产模式 ✅
```

### 场景2：验证失败
```
正常生产模式（队列有数据） → 包装返修模式 ❌ → 回退到正常生产模式
包装返修模式（拍照队列有数据） → 正常生产模式 ❌ → 回退到包装返修模式
```

## 🔍 用户提示信息

### 进入包装返修模式失败
```
正常生产还没有结束，不能进行包装返修。
拍照队列中还有 X 个条码
小盒队列中还有 X 个条码
大箱队列中还有 X 个盒号
```

### 进入正常生产模式失败
```
包装返修还没有结束，不能切换到正常生产模式。
拍照队列中还有 X 个条码
请等待拍照队列清空后再切换到正常生产模式。
```

## 📝 日志记录

### 成功切换
- `✅ 已进入包装返修模式`
- `✅ 已切换到正常生产模式`

### 失败切换
- `❌ 进入包装返修模式失败: [错误信息]`
- `❌ 切换到正常生产模式失败: [错误信息]`

### 模式说明
每次成功切换后都会显示当前模式的功能说明，帮助用户了解模式特性。

## ⚠️ 注意事项

1. **异步验证**：所有验证都在后台线程执行，不会阻塞UI
2. **自动回退**：验证失败时自动回退到原模式，保证状态一致性
3. **异常处理**：完善的异常处理机制，确保程序稳定性
4. **用户友好**：清晰的提示信息，帮助用户理解失败原因

这个验证机制确保了生产模式切换的安全性，防止了数据丢失和状态不一致的问题。
