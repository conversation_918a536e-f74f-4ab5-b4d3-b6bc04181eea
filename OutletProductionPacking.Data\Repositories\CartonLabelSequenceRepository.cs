using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public class CartonLabelSequenceRepository : ICartonLabelSequenceRepository
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public CartonLabelSequenceRepository(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<CartonLabelSequence> GetOrCreateAsync(string dateCode)
        {
            using var context = _contextFactory.CreateDbContext();

            var sequence = await context.CartonLabelSequences
                .FirstOrDefaultAsync(x => x.DateCode == dateCode);

            if (sequence == null)
            {
                sequence = new CartonLabelSequence
                {
                    DateCode = dateCode,
                    CurrentSequence = 0,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                context.CartonLabelSequences.Add(sequence);
                await context.SaveChangesAsync();
            }

            return sequence;
        }

        public async Task<int> GetNextSequenceAsync(string dateCode)
        {
            using var context = _contextFactory.CreateDbContext();

            // 使用事务确保线程安全
            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                var sequence = await context.CartonLabelSequences
                    .FirstOrDefaultAsync(x => x.DateCode == dateCode);

                if (sequence == null)
                {
                    sequence = new CartonLabelSequence
                    {
                        DateCode = dateCode,
                        CurrentSequence = 1,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    context.CartonLabelSequences.Add(sequence);
                }
                else
                {
                    sequence.CurrentSequence++;
                    sequence.UpdatedAt = DateTime.Now;
                    context.CartonLabelSequences.Update(sequence);
                }

                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                return sequence.CurrentSequence;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
