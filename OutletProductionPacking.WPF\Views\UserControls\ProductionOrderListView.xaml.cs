using OutletProductionPacking.ViewModels.ProductionOrderManage;
using System.Windows.Controls;
using System.Windows.Input;

namespace OutletProductionPacking.WPF.Views
{
    public partial class ProductionOrderListView : UserControl
    {
        public ProductionOrderListView()
        {
            InitializeComponent();
            DataContext = App.GetService<ProductionOrderListViewModel>();
        }

        private void DataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is ProductionOrderListViewModel viewModel &&
                viewModel.SelectedOrder != null)
            {
                // 双击行时，调用查看条码命令
                viewModel.ViewBarcodesCommand.Execute(viewModel.SelectedOrder);
            }
        }
    }
}
