﻿using NModbus;
using System.Net.Sockets;

namespace OutletProductionPacking.Core.Services
{
    public class ModbusService : IModbusService, IDisposable
    {
        private TcpClient? _client;
        private IModbusMaster? _master;
        private const byte SlaveId = 1; // EBYTE模块默认站号为1

        public bool IsConnected => _client?.Connected ?? false;

        public async Task<bool> ConnectAsync(string ipAddress, int port = 502)
        {
            try
            {
                _client = new TcpClient();
                await _client.ConnectAsync(ipAddress, port);

                var factory = new ModbusFactory();
                _master = factory.CreateMaster(_client);

                return true;
            }
            catch (Exception)
            {
                Disconnect();
                return false;
            }
        }

        public void Disconnect()
        {
            _master?.Dispose();
            _master = null;

            if (_client?.Connected ?? false)
            {
                _client.Close();
            }
            _client?.Dispose();
            _client = null;
        }

        public async Task<bool[]> ReadInputsAsync(ushort startAddress, ushort count)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DI对应功能码02
            var result = await _master.ReadInputsAsync(SlaveId, startAddress, count);
            return result;
        }

        public async Task<bool> WriteCoilAsync(ushort coilAddress, bool value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DO对应功能码05
            await _master.WriteSingleCoilAsync(SlaveId, coilAddress, value);
            return true;
        }
        public async Task<bool> WriteRegisterAsync(ushort registerAddress, ushort value)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DO对应功能码05
            await _master.WriteSingleRegisterAsync(SlaveId, registerAddress, value);
            return true;
        }

        public async Task WriteCoilsAsync(ushort startAddress, bool[] values)
        {
            if (_master == null) throw new InvalidOperationException("未连接到设备");

            // EBYTE模块的DO对应功能码0F
            await _master.WriteMultipleCoilsAsync(SlaveId, startAddress, values);
        }

        public void Dispose()
        {
            Disconnect();
        }
    }
}
