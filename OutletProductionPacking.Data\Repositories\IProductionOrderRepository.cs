using OutletProductionPacking.Data.Models;

namespace OutletProductionPacking.Data.Repositories
{
    public interface IProductionOrderRepository
    {
        Task<List<ProductionOrder>> GetAllAsync();
        Task<List<ProductionOrder>> GetPagedAsync(int pageNumber, int pageSize, string searchTerm = null);
        Task<List<ProductionOrder>> GetPagedAsync(int pageNumber, int pageSize, OrderSearchParams searchParams);
        Task<int> GetTotalCountAsync(string searchTerm = null);
        Task<int> GetTotalCountAsync(OrderSearchParams searchParams);
        Task<ProductionOrder> GetByIdAsync(int id);
        Task<ProductionOrder> GetByOrderNumberAsync(string orderNumber);
        Task<ProductionOrder> GetByOrderNumberAndProductCodeAsync(string orderNumber, string productCode);
        Task<string> GenerateOrderNumberAsync();
        Task<ProductionOrder> AddAsync(ProductionOrder order);
        Task<ProductionOrder> UpdateAsync(ProductionOrder order);
        Task DeleteAsync(int id);
        Task<List<ProductionOrder>> GetUncompletedOrdersAsync();
        Task<int> GetCompletedQuantityAsync(int orderId);
        Task UpdateOrderFinishedStatusAsync(int orderId);
    }
}
