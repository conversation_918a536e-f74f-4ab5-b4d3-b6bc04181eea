<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BarTender 打印服务测试客户端</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #007acc;
        }
        input, select, textarea, button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007acc;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #005a9e;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ BarTender 打印服务测试客户端</h1>
        
        <div class="section">
            <h3>🔧 服务配置</h3>
            <label>服务地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
        </div>

        <div class="section">
            <h3>📊 服务状态</h3>
            <button onclick="checkStatus()">检查服务状态</button>
            <div id="statusResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>🖨️ 获取打印机列表</h3>
            <button onclick="getPrinters()">获取打印机</button>
            <div id="printersResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📄 获取模板列表</h3>
            <button onclick="getTemplates()">获取模板</button>
            <div id="templatesResult" class="result" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>🏷️ 打印测试</h3>
            <label>模板名称:</label>
            <input type="text" id="templateName" value="BoxTemplate.btw" placeholder="BoxTemplate.btw">
            
            <label>打印机名称 (可选):</label>
            <input type="text" id="printerName" placeholder="留空使用默认打印机">
            
            <label>打印份数:</label>
            <input type="number" id="copies" value="1" min="1" max="10">
            
            <label>打印参数 (JSON格式):</label>
            <textarea id="parameters" rows="6" placeholder='{"产品型号": "ABC123", "生产日期": "2025-05-30", "条码": "123456789"}'>{
  "产品型号": "ABC123",
  "生产日期": "2025-05-30",
  "条码": "123456789",
  "数量": "100"
}</textarea>
            
            <button onclick="printLabel()">执行打印</button>
            <div id="printResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        function getServerUrl() {
            return document.getElementById('serverUrl').value.trim();
        }

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        async function checkStatus() {
            try {
                const response = await fetch(`${getServerUrl()}/status`);
                const data = await response.json();
                showResult('statusResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('statusResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getPrinters() {
            try {
                const response = await fetch(`${getServerUrl()}/printers`);
                const data = await response.json();
                showResult('printersResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('printersResult', `错误: ${error.message}`, 'error');
            }
        }

        async function getTemplates() {
            try {
                const response = await fetch(`${getServerUrl()}/templates`);
                const data = await response.json();
                showResult('templatesResult', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                showResult('templatesResult', `错误: ${error.message}`, 'error');
            }
        }

        async function printLabel() {
            try {
                const templateName = document.getElementById('templateName').value.trim();
                const printerName = document.getElementById('printerName').value.trim();
                const copies = parseInt(document.getElementById('copies').value);
                const parametersText = document.getElementById('parameters').value.trim();

                if (!templateName) {
                    showResult('printResult', '请输入模板名称', 'error');
                    return;
                }

                let parameters = {};
                if (parametersText) {
                    try {
                        parameters = JSON.parse(parametersText);
                    } catch (e) {
                        showResult('printResult', `参数JSON格式错误: ${e.message}`, 'error');
                        return;
                    }
                }

                const printRequest = {
                    TemplateName: templateName,
                    Parameters: parameters,
                    PrinterName: printerName,
                    Copies: copies,
                    RequestId: generateUUID()
                };

                const response = await fetch(`${getServerUrl()}/print`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(printRequest)
                });

                const data = await response.json();
                const resultType = data.Success ? 'success' : 'error';
                showResult('printResult', JSON.stringify(data, null, 2), resultType);
            } catch (error) {
                showResult('printResult', `错误: ${error.message}`, 'error');
            }
        }

        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // 页面加载完成后自动检查服务状态
        window.onload = function() {
            setTimeout(checkStatus, 500);
        };
    </script>
</body>
</html>
