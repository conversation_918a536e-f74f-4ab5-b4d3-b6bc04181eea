using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 大箱与小盒关联表
    /// </summary>
    [Table("CartonBoxMappings")]
    public class CartonBoxMapping
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 大箱包装记录ID（外键）
        /// </summary>
        public int CartonPackageId { get; set; }

        /// <summary>
        /// 箱号（冗余字段，便于查询）
        /// </summary>
        [Required]
        [StringLength(10)]
        public string CartonNumber { get; set; } = string.Empty;

        /// <summary>
        /// 盒号
        /// </summary>
        [Required]
        [StringLength(11)]
        public string BoxNumber { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey("CartonPackageId")]
        public virtual CartonPackage CartonPackage { get; set; } = null!;
    }
}
