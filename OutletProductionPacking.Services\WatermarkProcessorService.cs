using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data.Repositories;
using OutletProductionPacking.Utils.Services;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace OutletProductionPacking.Services
{
    public class WatermarkProcessorService : IDisposable
    {
        private readonly IProductPhotoRepository _photoRepository;
        private readonly IWatermarkService _watermarkService;
        private readonly ILogService _logger;
        private readonly Timer _processingTimer;
        private readonly object _lockObject = new object();
        private bool _isDisposed = false;
        private bool _isProcessing = false;

        public WatermarkProcessorService(
            IProductPhotoRepository photoRepository,
            IWatermarkService watermarkService,
            ILogService logger)
        {
            _photoRepository = photoRepository;
            _watermarkService = watermarkService;
            _logger = logger;

            _logger.Info("水印处理服务启动");

            // 创建30秒间隔的定时器
            _processingTimer = new Timer(ProcessWatermarks, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// 处理水印的定时器回调
        /// </summary>
        private void ProcessWatermarks(object? state)
        {
            if (_isDisposed || _isProcessing) return;

            lock (_lockObject)
            {
                if (_isProcessing) return;
                _isProcessing = true;
            }

            try
            {
                ProcessUnwatermarkedPhotosAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "处理水印时发生异常");
            }
            finally
            {
                _isProcessing = false;
            }
        }

        /// <summary>
        /// 处理未添加水印的照片
        /// </summary>
        private void ProcessUnwatermarkedPhotosAsync()
        {
            try
            {
                // 获取需要处理的照片（最多100条）
                var photos = _photoRepository.GetUnwatermarkedPhotosAsync(100).Result;

                if (photos.Count == 0)
                {
                    return; // 没有需要处理的照片
                }

                _logger.Info($"开始处理 {photos.Count} 张照片的水印");

                foreach (var photo in photos)
                {
                    if (_isDisposed) break;

                    try
                    {
                        // 检查文件是否存在
                        if (!File.Exists(photo.PhotoPath))
                        {
                            _logger.Warn($"照片文件不存在: {photo.PhotoPath}");
                            _photoRepository.UpdateWatermarkStatusAsync(photo.Id, 2).Wait(); // 标记为失败
                            continue;
                        }

                        // 添加水印
                        bool success = _watermarkService.AddWatermarkAsync(photo.PhotoPath, photo.Barcode).Result;

                        // 更新状态
                        short status = success ? (short)1 : (short)2; // 1=成功，2=失败
                        _photoRepository.UpdateWatermarkStatusAsync(photo.Id, status).Wait();

                        if (success)
                        {
                            _logger.Info($"成功为照片添加水印: ID={photo.Id}, Barcode={photo.Barcode}");
                        }
                        else
                        {
                            _logger.Warn($"为照片添加水印失败: ID={photo.Id}, Barcode={photo.Barcode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, $"处理照片水印时发生异常: ID={photo.Id}, Barcode={photo.Barcode}");
                        _photoRepository.UpdateWatermarkStatusAsync(photo.Id, 2).Wait(); // 标记为失败
                    }
                }

                _logger.Info($"完成处理 {photos.Count} 张照片的水印");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "获取未处理水印的照片时发生异常");
            }
        }

        public void Dispose()
        {
            if (_isDisposed) return;

            _isDisposed = true;
            _processingTimer?.Dispose();
            _logger.Info("水印处理服务已停止");
        }
    }
}
