using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

namespace OutletProductionPacking.MockDevices.Services
{
    /// <summary>
    /// Windows虚拟COM端口服务 - 使用Windows设备管理器创建虚拟串口
    /// </summary>
    public class WindowsVirtualComService : IDisposable
    {
        private readonly string _portName;
        private bool _isCreated;
        private Process? _deviceManagerProcess;

        public event EventHandler<string>? StatusChanged;

        public bool IsCreated => _isCreated;
        public string PortName => _portName;

        public WindowsVirtualComService(string portName = "COM5")
        {
            _portName = portName;
        }

        /// <summary>
        /// 创建虚拟COM端口
        /// </summary>
        public async Task<bool> CreateVirtualPortAsync()
        {
            try
            {
                OnStatusChanged("正在创建虚拟COM端口...");

                // 方法1: 使用devcon工具创建虚拟串口
                if (await TryCreateWithDevconAsync())
                {
                    _isCreated = true;
                    OnStatusChanged($"虚拟COM端口 {_portName} 创建成功 (使用devcon)");
                    return true;
                }

                // 方法2: 使用PowerShell创建虚拟串口
                if (await TryCreateWithPowerShellAsync())
                {
                    _isCreated = true;
                    OnStatusChanged($"虚拟COM端口 {_portName} 创建成功 (使用PowerShell)");
                    return true;
                }

                // 方法3: 使用注册表创建虚拟串口
                if (await TryCreateWithRegistryAsync())
                {
                    _isCreated = true;
                    OnStatusChanged($"虚拟COM端口 {_portName} 创建成功 (使用注册表)");
                    return true;
                }

                OnStatusChanged("所有方法都失败，无法创建虚拟COM端口");
                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"创建虚拟COM端口失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TryCreateWithDevconAsync()
        {
            try
            {
                // 查找devcon.exe
                var devconPath = FindDevconTool();
                if (string.IsNullOrEmpty(devconPath))
                {
                    OnStatusChanged("未找到devcon工具");
                    return false;
                }

                // 使用devcon安装虚拟串口设备
                var startInfo = new ProcessStartInfo
                {
                    FileName = devconPath,
                    Arguments = "install null.inf Root\\NULL",
                    UseShellExecute = true,
                    Verb = "runas", // 以管理员权限运行
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"devcon方法失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TryCreateWithPowerShellAsync()
        {
            try
            {
                // 使用PowerShell创建虚拟串口
                var script = $@"
                    # 创建虚拟串口的PowerShell脚本
                    $portName = '{_portName}'
                    
                    # 检查端口是否已存在
                    $existingPort = Get-WmiObject -Class Win32_SerialPort | Where-Object {{ $_.DeviceID -eq $portName }}
                    if ($existingPort) {{
                        Write-Host ""端口 $portName 已存在""
                        exit 0
                    }}
                    
                    # 尝试创建虚拟串口
                    try {{
                        # 使用WMI创建虚拟设备
                        $deviceManager = Get-WmiObject -Class Win32_SystemDriver | Where-Object {{ $_.Name -eq 'serial' }}
                        if ($deviceManager) {{
                            Write-Host ""找到串口驱动""
                        }}
                        exit 0
                    }} catch {{
                        Write-Host ""创建失败: $($_.Exception.Message)""
                        exit 1
                    }}
                ";

                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-ExecutionPolicy Bypass -Command \"{script}\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"PowerShell方法失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> TryCreateWithRegistryAsync()
        {
            try
            {
                OnStatusChanged("尝试使用注册表方法创建虚拟串口...");

                // 创建批处理文件来添加注册表项
                var batchContent = $@"
@echo off
echo 正在创建虚拟串口 {_portName}...

REM 添加串口设备到注册表
reg add ""HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000"" /v ""DeviceDesc"" /t REG_SZ /d ""虚拟串口 {_portName}"" /f
reg add ""HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000"" /v ""HardwareID"" /t REG_MULTI_SZ /d ""ROOT\PORTS"" /f
reg add ""HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000"" /v ""Service"" /t REG_SZ /d ""Serial"" /f
reg add ""HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000"" /v ""Class"" /t REG_SZ /d ""Ports"" /f
reg add ""HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000"" /v ""ClassGUID"" /t REG_SZ /d ""{{4D36E978-E325-11CE-BFC1-08002BE10318}}"" /f

REM 添加串口映射
reg add ""HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM"" /v ""\Device\VirtualSerial0"" /t REG_SZ /d ""{_portName}"" /f

echo 虚拟串口创建完成
echo 请重启计算机或重新扫描硬件以使更改生效
";

                var batchFile = Path.Combine(Path.GetTempPath(), "create_virtual_com.bat");
                await File.WriteAllTextAsync(batchFile, batchContent);

                var startInfo = new ProcessStartInfo
                {
                    FileName = batchFile,
                    UseShellExecute = true,
                    Verb = "runas",
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    
                    // 清理临时文件
                    try { File.Delete(batchFile); } catch { }
                    
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"注册表方法失败: {ex.Message}");
                return false;
            }
        }

        private string FindDevconTool()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files (x86)\Windows Kits\10\Tools\x64\devcon.exe",
                @"C:\Program Files (x86)\Windows Kits\10\Tools\x86\devcon.exe",
                @"C:\Windows\System32\devcon.exe",
                @".\Tools\devcon.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// 检查虚拟COM端口是否存在
        /// </summary>
        public bool CheckPortExists()
        {
            try
            {
                var availablePorts = System.IO.Ports.SerialPort.GetPortNames();
                var exists = Array.Exists(availablePorts, port => port.Equals(_portName, StringComparison.OrdinalIgnoreCase));
                
                OnStatusChanged($"检查端口 {_portName}: {(exists ? "存在" : "不存在")}");
                return exists;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"检查端口时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除虚拟COM端口
        /// </summary>
        public async Task<bool> RemoveVirtualPortAsync()
        {
            try
            {
                if (!_isCreated)
                {
                    OnStatusChanged("虚拟端口未创建，无需删除");
                    return true;
                }

                OnStatusChanged($"正在删除虚拟COM端口 {_portName}...");

                // 创建删除脚本
                var batchContent = $@"
@echo off
echo 正在删除虚拟串口 {_portName}...

REM 删除注册表项
reg delete ""HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\Root\PORTS\0000"" /f
reg delete ""HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\SERIALCOMM"" /v ""\Device\VirtualSerial0"" /f

echo 虚拟串口删除完成
";

                var batchFile = Path.Combine(Path.GetTempPath(), "remove_virtual_com.bat");
                await File.WriteAllTextAsync(batchFile, batchContent);

                var startInfo = new ProcessStartInfo
                {
                    FileName = batchFile,
                    UseShellExecute = true,
                    Verb = "runas",
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    
                    // 清理临时文件
                    try { File.Delete(batchFile); } catch { }
                    
                    _isCreated = false;
                    OnStatusChanged($"虚拟COM端口 {_portName} 已删除");
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"删除虚拟COM端口失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 刷新硬件设备
        /// </summary>
        public async Task RefreshHardwareAsync()
        {
            try
            {
                OnStatusChanged("正在刷新硬件设备...");

                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-Command \"Get-PnpDevice | Where-Object {$_.Class -eq 'Ports'} | Enable-PnpDevice -Confirm:$false\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    OnStatusChanged("硬件设备刷新完成");
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"刷新硬件设备失败: {ex.Message}");
            }
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, $"[{DateTime.Now:HH:mm:ss}] {message}");
        }

        public void Dispose()
        {
            if (_isCreated)
            {
                _ = Task.Run(async () => await RemoveVirtualPortAsync());
            }
        }
    }
}
