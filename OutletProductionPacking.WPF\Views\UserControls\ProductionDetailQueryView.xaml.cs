using OutletProductionPacking.ViewModels.StatisticalReports;
using OutletProductionPacking.Core.DTOs;
using System.Windows.Controls;
using System.Windows.Input;

namespace OutletProductionPacking.WPF.Views.UserControls
{
    public partial class ProductionDetailQueryView : UserControl
    {
        public ProductionDetailQueryView()
        {
            InitializeComponent();
            DataContext = App.GetService<ProductionDetailQueryViewModel>();
        }

        private void DataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is ProductionDetailQueryViewModel viewModel &&
                viewModel.SelectedOrderSummary != null)
            {
                // 双击行时，调用查看条码明细命令
                viewModel.ViewBarcodeDetailsCommand.Execute(viewModel.SelectedOrderSummary);
            }
        }

        private void PhotoPath_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is System.Windows.FrameworkElement element &&
                element.DataContext is BarcodeDetailDto barcodeDetail &&
                DataContext is ProductionDetailQueryViewModel viewModel)
            {
                // 点击照片路径时，调用查看照片命令
                if (!string.IsNullOrEmpty(barcodeDetail.PhotoPath))
                {
                    viewModel.ViewPhotoCommand.Execute(barcodeDetail.PhotoPath);
                }
            }
        }
    }
}
