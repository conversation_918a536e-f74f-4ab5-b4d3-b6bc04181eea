<UserControl x:Class="OutletProductionPacking.WPF.Views.ProductionOrderListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <!-- 查询条件部分 -->
                <ColumnDefinition Width="Auto"/> <!-- 订单号标签 -->
                <ColumnDefinition Width="120"/> <!-- 订单号输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 产品编码标签 -->
                <ColumnDefinition Width="100"/> <!-- 产品编码输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 产品名称标签 -->
                <ColumnDefinition Width="100"/> <!-- 产品名称输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 规格型号标签 -->
                <ColumnDefinition Width="100"/> <!-- 规格型号输入框 -->
                <ColumnDefinition Width="Auto"/> <!-- 开始日期标签 -->
                <ColumnDefinition Width="110"/> <!-- 开始日期选择器 -->
                <ColumnDefinition Width="Auto"/> <!-- 结束日期标签 -->
                <ColumnDefinition Width="110"/> <!-- 结束日期选择器 -->
                <ColumnDefinition Width="Auto"/> <!-- 搜索按钮 -->
                <ColumnDefinition Width="Auto"/> <!-- 清除按钮 -->
                <ColumnDefinition Width="*"/> <!-- 弹性空间 -->
                <ColumnDefinition Width="Auto"/> <!-- 新增按钮 -->
                <ColumnDefinition Width="Auto"/> <!-- 删除按钮 -->
            </Grid.ColumnDefinitions>

            <!-- 订单号 -->
            <TextBlock Grid.Column="0" Text="订单号:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="1" Text="{Binding OrderNumberSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 产品编码 -->
            <TextBlock Grid.Column="2" Text="产品编码:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="3" Text="{Binding ProductCodeSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 产品名称 -->
            <TextBlock Grid.Column="4" Text="产品名称:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="5" Text="{Binding ProductNameSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 规格型号 -->
            <TextBlock Grid.Column="6" Text="规格型号:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="7" Text="{Binding ProductSpecificationSearch, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 开始日期 -->
            <TextBlock Grid.Column="8" Text="开始日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="9" SelectedDate="{Binding StartDate}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 结束日期 -->
            <TextBlock Grid.Column="10" Text="结束日期:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <DatePicker Grid.Column="11" SelectedDate="{Binding EndDate}" Margin="0,0,5,0" VerticalAlignment="Center" Height="24"/>

            <!-- 搜索和清除按钮 -->
            <Button Grid.Column="12" Content="搜索" Command="{Binding LoadOrdersCommand}" Style="{StaticResource PrimaryButtonStyle}" Padding="10,2" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="13" Content="清除" Command="{Binding ClearSearchCommand}" Style="{StaticResource DefaultButtonStyle}" Padding="10,2" Margin="0,0,5,0" VerticalAlignment="Center"/>

            <!-- 新增和删除按钮放在右边 -->
            <Button Grid.Column="15" Content="新增" Command="{Binding AddOrderCommand}" Style="{StaticResource PrimaryButtonStyle}" Margin="0,0,5,0" VerticalAlignment="Center"/>
            <Button Grid.Column="16" Content="删除" Command="{Binding DeleteOrderCommand}" CommandParameter="{Binding SelectedOrder}" Style="{StaticResource DangerButtonStyle}" Margin="0" VerticalAlignment="Center"/>
        </Grid>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="1" ItemsSource="{Binding Orders}" SelectedItem="{Binding SelectedOrder}" AutoGenerateColumns="False" IsReadOnly="True" GridLinesVisibility="All"
             CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False" CanUserResizeColumns="True" CanUserResizeRows="False" CanUserSortColumns="True"
             SelectionMode="Single" SelectionUnit="FullRow" Margin="10" MouseDoubleClick="DataGrid_MouseDoubleClick">
            <DataGrid.Columns>
                <DataGridTextColumn Header="订单号" Binding="{Binding OrderNumber}" Width="120"/>
                <DataGridTextColumn Header="产品编码" Binding="{Binding ProductCode}" Width="120"/>
                <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="300"/>
                <DataGridTextColumn Header="规格型号" Binding="{Binding ProductSpecification}" Width="300"/>
                <DataGridTextColumn Header="计划数量" Binding="{Binding Quantity}" Width="80"/>
                <DataGridTextColumn Header="创建时间" Binding="{Binding CreatedAt, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页控件 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="{Binding TotalItems, StringFormat={}共 {0} 条记录}" VerticalAlignment="Center" Margin="0,0,20,0"/>
                <TextBlock Text="每页行数:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <ComboBox Width="60" SelectedItem="{Binding PageSize}" Margin="0,0,5,0">
                    <ComboBoxItem Content="20"/>
                    <ComboBoxItem Content="50"/>
                    <ComboBoxItem Content="100"/>
                    <ComboBoxItem Content="200"/>
                </ComboBox>
            </StackPanel>

            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="首页" Command="{Binding FirstPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" Padding="10,2"/>
                <Button Content="上一页" Command="{Binding PreviousPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="0,0,5,0" Padding="10,2"/>
                <TextBox Width="40" Text="{Binding CurrentPage, UpdateSourceTrigger=PropertyChanged}" VerticalAlignment="Center" Margin="5,0" TextAlignment="Center"/>
                <TextBlock Text="{Binding TotalPages, StringFormat={}/ {0} 页}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <Button Content="跳转" Command="{Binding LoadOrdersCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0" Padding="10,2"/>
                <Button Content="下一页" Command="{Binding NextPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0,0,0" Padding="10,2"/>
                <Button Content="尾页" Command="{Binding LastPageCommand}" Style="{StaticResource DefaultButtonStyle}" Margin="5,0,0,0" Padding="10,2"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
