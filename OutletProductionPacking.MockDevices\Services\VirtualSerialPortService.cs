using System;
using System.Diagnostics;
using System.IO;
using System.IO.Ports;
using System.Threading;
using System.Threading.Tasks;
using System.Text;

namespace OutletProductionPacking.MockDevices.Services
{
    /// <summary>
    /// 虚拟串口服务 - 创建真正可连接的COM端口
    /// </summary>
    public class VirtualSerialPortService : IDisposable
    {
        private Process? _virtualPortProcess;
        private SerialPort? _virtualPort;
        private readonly string _portName;
        private readonly int _baudRate;
        private bool _isRunning;
        private CancellationTokenSource? _cancellationTokenSource;
        private readonly string _virtualPortToolPath;

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<string>? DataReceived;

        public bool IsRunning => _isRunning;
        public string PortName => _portName;

        public VirtualSerialPortService(string portName = "COM5", int baudRate = 9600)
        {
            _portName = portName;
            _baudRate = baudRate;
            
            // 查找虚拟串口工具路径
            _virtualPortToolPath = FindVirtualPortTool();
        }

        private string FindVirtualPortTool()
        {
            // 常见的虚拟串口工具路径
            var possiblePaths = new[]
            {
                @"C:\Program Files\com0com\setupc.exe",
                @"C:\Program Files (x86)\com0com\setupc.exe",
                @".\Tools\com0com\setupc.exe",
                @".\com0com\setupc.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }

            return string.Empty;
        }

        public async Task<bool> StartAsync()
        {
            if (_isRunning) return true;

            try
            {
                OnStatusChanged("正在启动虚拟串口服务...");

                // 方法1: 尝试使用com0com创建虚拟串口对
                if (!string.IsNullOrEmpty(_virtualPortToolPath))
                {
                    if (await CreateVirtualPortPairAsync())
                    {
                        OnStatusChanged($"虚拟串口对创建成功: {_portName}");
                        return await StartVirtualPortAsync();
                    }
                }

                // 方法2: 使用内置的命名管道模拟串口
                return await StartNamedPipePortAsync();
            }
            catch (Exception ex)
            {
                OnStatusChanged($"启动虚拟串口失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> CreateVirtualPortPairAsync()
        {
            try
            {
                // 使用com0com创建虚拟串口对
                var startInfo = new ProcessStartInfo
                {
                    FileName = _virtualPortToolPath,
                    Arguments = $"install PortName={_portName} PortName=COM99",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"创建虚拟串口对失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> StartVirtualPortAsync()
        {
            try
            {
                _virtualPort = new SerialPort(_portName, _baudRate, Parity.None, 8, StopBits.One)
                {
                    ReadTimeout = 1000,
                    WriteTimeout = 1000,
                    DtrEnable = true,
                    RtsEnable = true
                };

                _virtualPort.DataReceived += VirtualPort_DataReceived;
                _virtualPort.Open();

                _isRunning = true;
                _cancellationTokenSource = new CancellationTokenSource();

                OnStatusChanged($"虚拟串口 {_portName} 已启动 (波特率: {_baudRate})");

                // 启动数据处理任务
                _ = Task.Run(() => ProcessDataAsync(_cancellationTokenSource.Token));

                return true;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"启动虚拟串口失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> StartNamedPipePortAsync()
        {
            try
            {
                OnStatusChanged("使用命名管道模拟串口通信...");
                
                _isRunning = true;
                _cancellationTokenSource = new CancellationTokenSource();

                // 启动命名管道服务器
                _ = Task.Run(() => StartNamedPipeServerAsync(_cancellationTokenSource.Token));

                OnStatusChanged($"命名管道串口模拟器已启动 (模拟端口: {_portName})");
                return true;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"启动命名管道串口失败: {ex.Message}");
                return false;
            }
        }

        private async Task StartNamedPipeServerAsync(CancellationToken cancellationToken)
        {
            var pipeName = $"VirtualSerial_{_portName}";
            
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    using var pipeServer = new System.IO.Pipes.NamedPipeServerStream(
                        pipeName,
                        System.IO.Pipes.PipeDirection.InOut,
                        1,
                        System.IO.Pipes.PipeTransmissionMode.Byte,
                        System.IO.Pipes.PipeOptions.Asynchronous);

                    OnStatusChanged($"等待客户端连接到命名管道: {pipeName}");
                    
                    await pipeServer.WaitForConnectionAsync(cancellationToken);
                    OnStatusChanged("客户端已连接到命名管道");

                    // 处理客户端通信
                    await HandlePipeClientAsync(pipeServer, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"命名管道服务器错误: {ex.Message}");
                    await Task.Delay(1000, cancellationToken);
                }
            }
        }

        private async Task HandlePipeClientAsync(System.IO.Pipes.NamedPipeServerStream pipeServer, CancellationToken cancellationToken)
        {
            var buffer = new byte[1024];
            
            try
            {
                while (pipeServer.IsConnected && !cancellationToken.IsCancellationRequested)
                {
                    var bytesRead = await pipeServer.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead > 0)
                    {
                        var receivedData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                        OnStatusChanged($"收到数据: {receivedData}");
                        OnDataReceived(receivedData);

                        // 模拟响应
                        await SendResponseAsync(pipeServer, receivedData);
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理管道客户端时发生错误: {ex.Message}");
            }
        }

        private async Task SendResponseAsync(System.IO.Pipes.NamedPipeServerStream pipeServer, string receivedData)
        {
            try
            {
                // 根据接收到的命令发送相应的响应
                string response = GenerateResponse(receivedData);
                var responseBytes = Encoding.UTF8.GetBytes(response);
                
                await pipeServer.WriteAsync(responseBytes, 0, responseBytes.Length);
                await pipeServer.FlushAsync();
                
                OnStatusChanged($"发送响应: {response}");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"发送响应失败: {ex.Message}");
            }
        }

        private string GenerateResponse(string command)
        {
            // 根据命令生成相应的响应
            // 这里可以根据实际的电子秤协议来实现
            if (command.Contains("weight") || command.Contains("重量"))
            {
                return "5.2kg\r\n"; // 模拟重量响应
            }
            
            return "OK\r\n"; // 默认响应
        }

        private void VirtualPort_DataReceived(object? sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_virtualPort?.IsOpen == true)
                {
                    var data = _virtualPort.ReadExisting();
                    OnStatusChanged($"收到串口数据: {data}");
                    OnDataReceived(data);
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理串口数据时发生错误: {ex.Message}");
            }
        }

        private async Task ProcessDataAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && _isRunning)
            {
                try
                {
                    // 定期发送心跳或状态信息
                    await Task.Delay(5000, cancellationToken);
                    
                    if (_virtualPort?.IsOpen == true)
                    {
                        OnStatusChanged("虚拟串口运行正常");
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"数据处理任务错误: {ex.Message}");
                }
            }
        }

        public async Task SendDataAsync(string data)
        {
            try
            {
                if (_virtualPort?.IsOpen == true)
                {
                    _virtualPort.Write(data);
                    OnStatusChanged($"发送数据: {data}");
                }
                else
                {
                    OnStatusChanged("虚拟串口未打开，无法发送数据");
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"发送数据失败: {ex.Message}");
            }
        }

        public void Stop()
        {
            if (!_isRunning) return;

            _isRunning = false;
            _cancellationTokenSource?.Cancel();

            try
            {
                _virtualPort?.Close();
                _virtualPort?.Dispose();
                _virtualPort = null;

                OnStatusChanged("虚拟串口服务已停止");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"停止虚拟串口时发生错误: {ex.Message}");
            }
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, $"[{DateTime.Now:HH:mm:ss}] {message}");
        }

        private void OnDataReceived(string data)
        {
            DataReceived?.Invoke(this, data);
        }

        public void Dispose()
        {
            Stop();
            _cancellationTokenSource?.Dispose();
        }
    }
}
