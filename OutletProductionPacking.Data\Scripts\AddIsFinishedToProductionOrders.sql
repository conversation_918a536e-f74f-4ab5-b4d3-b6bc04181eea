-- 添加IsFinished字段到ProductionOrders表
ALTER TABLE `ProductionOrders` ADD COLUMN `IsFinished` TINYINT(1) NOT NULL DEFAULT 0;

-- 更新已完成订单的状态
-- 查找所有订单，如果订单的所有条码都已生产，则将订单标记为已完成
UPDATE `ProductionOrders` po
SET po.`IsFinished` = 1
WHERE (
    SELECT COUNT(*) 
    FROM `ProductionOrderBarcodes` pob 
    WHERE pob.`OrderId` = po.`Id` AND pob.`IsProduced` = 0
) = 0
AND (
    SELECT COUNT(*) 
    FROM `ProductionOrderBarcodes` pob 
    WHERE pob.`OrderId` = po.`Id`
) > 0;
