using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 大箱包装记录表
    /// </summary>
    [Table("CartonPackages")]
    public class CartonPackage
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 箱号（YYMMDD+4位序号）
        /// </summary>
        [Required]
        [StringLength(10)]
        public string CartonNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 重量（kg，保留1位小数）
        /// </summary>
        [Column(TypeName = "decimal(10,1)")]
        public decimal Weight { get; set; }

        /// <summary>
        /// 产品总数量（这一箱的条码总数）
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// 盒数（装了几个小盒）
        /// </summary>
        public int BoxCount { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime ProductionDate { get; set; }

        /// <summary>
        /// 操作员ID
        /// </summary>
        public int OperatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey("OrderId")]
        public virtual ProductionOrder Order { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        [ForeignKey("OperatorId")]
        public virtual User Operator { get; set; } = null!;

        /// <summary>
        /// 箱号与盒号的关联
        /// </summary>
        public virtual ICollection<CartonBoxMapping> CartonBoxMappings { get; set; } = new List<CartonBoxMapping>();
    }
}
