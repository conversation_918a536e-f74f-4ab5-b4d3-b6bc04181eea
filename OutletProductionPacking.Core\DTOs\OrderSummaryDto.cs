using System;

namespace OutletProductionPacking.Core.DTOs
{
    /// <summary>
    /// 订单汇总数据传输对象
    /// </summary>
    public class OrderSummaryDto
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 产品编码
        /// </summary>
        public string ProductCode { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string ProductSpecification { get; set; } = string.Empty;

        /// <summary>
        /// 计划数量
        /// </summary>
        public int PlannedQuantity { get; set; }

        /// <summary>
        /// 已生产数量
        /// </summary>
        public int ProducedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 合格率（%）
        /// </summary>
        public decimal QualificationRate { get; set; }

        /// <summary>
        /// 已装盒数量
        /// </summary>
        public int BoxedQuantity { get; set; }

        /// <summary>
        /// 已装箱数量
        /// </summary>
        public int CartonedQuantity { get; set; }

        /// <summary>
        /// 生产开始时间
        /// </summary>
        public DateTime? ProductionStartTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// 订单创建日期
        /// </summary>
        public DateTime OrderCreatedAt { get; set; }

        /// <summary>
        /// 完成状态
        /// </summary>
        public string CompletionStatus { get; set; } = string.Empty;

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsFinished { get; set; }
    }
}
