# 生产作业界面升级说明

## 升级内容概述

根据用户需求，对生产作业界面进行了以下升级：

### 1. 历史记录按订单加载
- **原来**：一上来就加载最近生产的历史记录
- **现在**：只有选择订单并点确认后才加载所选择订单的历史记录
- **功能**：如果选了订单A，加载订单A的历史；切换到订单B时，加载订单B的历史

### 2. 订单确认时的队列校验
- **校验内容**：检查以下队列是否有条码
  - 成品拍照工序队列
  - 小盒贴队列
  - 大箱称重队列
- **校验结果**：如果任何队列中有条码，则不允许换单，提示用户等待当前产品完成生产

### 3. 生产模式选择
- **新增下拉框**：在订单选择下拉框旁边添加生产模式选择
- **选项**：
  - 正常生产
  - 包装返修

### 4. 包装返修模式功能
#### 4.1 进入条件校验
- 检查拍照队列、小盒队列、大箱队列是否为空
- 如果不为空，提示"正常生产还没有结束，不能进行包装返修"

#### 4.2 包装返修模式特性
- **质检扫码枪**：忽略所有扫码输入
- **包装拍照扫码枪**：正常工作，可以扫码入队
- **拍照流程**：扫码 → 包装 → 拍照 → 显示在历史列表
- **流程终止**：拍照完成后不进入小盒贴工序，流程结束

## 技术实现细节

### 1. 数据访问层修改
- `IQualityInspectionRepository`：新增 `GetRecentInspectionsByOrderIdAsync` 方法
- `IProductPhotoRepository`：新增 `GetRecentPhotosByOrderIdAsync` 方法
- 实现按订单ID过滤的历史记录查询

### 2. ViewModel修改
- 新增生产模式相关属性：
  - `ProductionModes`：生产模式选项集合
  - `SelectedProductionMode`：当前选择的生产模式
  - `IsPackageRepairMode`：是否为包装返修模式
- 新增方法：
  - `OnSelectedProductionModeChanged`：生产模式变化处理
  - `ValidateAndEnterPackageRepairModeAsync`：校验并进入包装返修模式
  - `LoadRecentInspectionsByOrderAsync`：按订单加载检测历史
  - `LoadRecentPhotosByOrderAsync`：按订单加载拍照历史

### 3. 界面修改
- 在订单选择区域添加生产模式下拉框
- 调整布局以容纳新的控件

### 4. 业务逻辑修改
- **质检扫码枪处理**：在包装返修模式下忽略扫码
- **拍照完成处理**：在包装返修模式下不进入小盒贴工序
- **订单确认**：添加队列校验逻辑
- **历史记录加载**：改为按订单过滤

## 使用说明

### 正常生产模式
1. 选择订单并点击确认
2. 系统加载该订单的历史记录
3. 按正常流程进行：质检 → 包装拍照 → 小盒贴 → 大箱称重

### 包装返修模式
1. 确保正常生产已完成（所有队列为空）
2. 选择"包装返修"模式
3. 系统校验队列状态
4. 进入包装返修模式后：
   - 质检扫码枪不响应
   - 包装拍照扫码枪正常工作
   - 拍照完成后流程结束

## 注意事项

1. **队列校验**：换单前必须确保所有队列为空
2. **模式切换**：包装返修模式有严格的进入条件
3. **历史记录**：现在按订单显示，更加精确
4. **流程控制**：包装返修模式下流程简化，避免重复工序

## 测试建议

1. 测试订单切换时的队列校验
2. 测试包装返修模式的进入条件
3. 测试包装返修模式下的扫码枪行为
4. 测试历史记录按订单过滤的功能
5. 测试拍照完成后的流程分支
