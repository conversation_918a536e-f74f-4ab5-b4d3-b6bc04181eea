using System;
using System.IO;
using System.IO.Pipes;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using System.Net.Sockets;
using System.Net;

namespace OutletProductionPacking.MockDevices.Services
{
    /// <summary>
    /// 简单虚拟COM服务 - 使用TCP端口模拟串口通信
    /// </summary>
    public class SimpleVirtualComService : IDisposable
    {
        private TcpListener? _tcpListener;
        private readonly int _port;
        private readonly string _comPortName;
        private bool _isRunning;
        private CancellationTokenSource? _cancellationTokenSource;
        private decimal _currentWeight = 5.2m;
        private string _currentUnit = "kg";

        public event EventHandler<string>? StatusChanged;
        public event EventHandler<string>? DataReceived;

        public bool IsRunning => _isRunning;
        public string ComPortName => _comPortName;
        public int TcpPort => _port;

        public decimal CurrentWeight
        {
            get => _currentWeight;
            set
            {
                _currentWeight = value;
                OnStatusChanged($"重量更新: {_currentWeight} {_currentUnit}");
            }
        }

        public string CurrentUnit
        {
            get => _currentUnit;
            set
            {
                _currentUnit = value;
                OnStatusChanged($"单位更新: {_currentUnit}");
            }
        }

        public SimpleVirtualComService(string comPortName = "COM5", int tcpPort = 5005)
        {
            _comPortName = comPortName;
            _port = tcpPort;
        }

        public async Task<bool> StartAsync()
        {
            if (_isRunning) return true;

            try
            {
                OnStatusChanged($"正在启动虚拟COM服务 (模拟 {_comPortName})...");

                _tcpListener = new TcpListener(IPAddress.Loopback, _port);
                _tcpListener.Start();

                _isRunning = true;
                _cancellationTokenSource = new CancellationTokenSource();

                // 启动TCP服务器任务
                _ = Task.Run(() => AcceptClientsAsync(_cancellationTokenSource.Token));

                OnStatusChanged($"✓ 虚拟COM服务已启动");
                OnStatusChanged($"  - 模拟串口: {_comPortName}");
                OnStatusChanged($"  - TCP端口: {_port}");
                OnStatusChanged($"  - 连接地址: localhost:{_port}");
                OnStatusChanged($"  - 当前重量: {_currentWeight} {_currentUnit}");

                return true;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"启动虚拟COM服务失败: {ex.Message}");
                return false;
            }
        }

        private async Task AcceptClientsAsync(CancellationToken cancellationToken)
        {
            OnStatusChanged("等待客户端连接...");

            while (!cancellationToken.IsCancellationRequested && _isRunning)
            {
                try
                {
                    if (_tcpListener != null)
                    {
                        var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                        OnStatusChanged($"客户端已连接: {tcpClient.Client.RemoteEndPoint}");

                        // 为每个客户端创建处理任务
                        _ = Task.Run(() => HandleClientAsync(tcpClient, cancellationToken));
                    }
                }
                catch (ObjectDisposedException)
                {
                    // TCP监听器已被释放，正常退出
                    break;
                }
                catch (Exception ex)
                {
                    if (!cancellationToken.IsCancellationRequested)
                    {
                        OnStatusChanged($"接受客户端连接时发生错误: {ex.Message}");
                        await Task.Delay(1000, cancellationToken);
                    }
                }
            }
        }

        private async Task HandleClientAsync(TcpClient client, CancellationToken cancellationToken)
        {
            var clientEndPoint = client.Client.RemoteEndPoint?.ToString() ?? "Unknown";
            
            try
            {
                using (client)
                using (var stream = client.GetStream())
                {
                    var buffer = new byte[1024];

                    while (client.Connected && !cancellationToken.IsCancellationRequested)
                    {
                        var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                        if (bytesRead > 0)
                        {
                            var receivedData = Encoding.UTF8.GetString(buffer, 0, bytesRead).Trim();
                            OnStatusChanged($"收到数据 [{clientEndPoint}]: {receivedData}");
                            OnDataReceived(receivedData);

                            // 生成响应
                            var response = GenerateResponse(receivedData);
                            var responseBytes = Encoding.UTF8.GetBytes(response);
                            
                            await stream.WriteAsync(responseBytes, 0, responseBytes.Length, cancellationToken);
                            await stream.FlushAsync(cancellationToken);
                            
                            OnStatusChanged($"发送响应 [{clientEndPoint}]: {response.Trim()}");
                        }
                        else
                        {
                            // 客户端断开连接
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"处理客户端 [{clientEndPoint}] 时发生错误: {ex.Message}");
            }
            finally
            {
                OnStatusChanged($"客户端 [{clientEndPoint}] 已断开连接");
            }
        }

        private string GenerateResponse(string command)
        {
            // 模拟电子秤的Modbus RTU响应
            command = command.ToLower().Trim();

            if (command.Contains("weight") || command.Contains("重量") || command.Contains("03"))
            {
                // 模拟读取重量寄存器的响应
                return $"{_currentWeight:F1}{_currentUnit}\r\n";
            }
            else if (command.Contains("unit") || command.Contains("单位"))
            {
                // 模拟读取单位的响应
                return $"{_currentUnit}\r\n";
            }
            else if (command.Contains("status") || command.Contains("状态"))
            {
                // 模拟状态查询响应
                return "STABLE\r\n"; // 稳定状态
            }
            else if (command.Contains("zero") || command.Contains("归零"))
            {
                // 模拟归零操作
                CurrentWeight = 0.0m;
                return "OK\r\n";
            }
            else if (command.Contains("tare") || command.Contains("去皮"))
            {
                // 模拟去皮操作
                return "OK\r\n";
            }
            else
            {
                // 默认响应 - 返回当前重量
                return $"{_currentWeight:F1}{_currentUnit}\r\n";
            }
        }

        public async Task SendDataAsync(string data)
        {
            try
            {
                OnStatusChanged($"广播数据: {data}");
                // 这里可以实现向所有连接的客户端广播数据
                // 暂时只记录日志
            }
            catch (Exception ex)
            {
                OnStatusChanged($"发送数据失败: {ex.Message}");
            }
        }

        public void SetWeight(decimal weight)
        {
            CurrentWeight = Math.Round(weight, 1);
        }

        public void SetUnit(string unit)
        {
            CurrentUnit = unit;
        }

        public void AddWeight(decimal increment)
        {
            CurrentWeight = Math.Round(Math.Max(0, CurrentWeight + increment), 1);
        }

        public void SimulateStableWeight(decimal weight)
        {
            SetWeight(weight);
            OnStatusChanged($"模拟稳定重量: {weight} {CurrentUnit}");
        }

        public void SimulateWeightFluctuation(decimal baseWeight, decimal range)
        {
            var random = new Random();
            var fluctuation = (decimal)(random.NextDouble() * 2 - 1) * range;
            var newWeight = Math.Max(0, baseWeight + fluctuation);
            CurrentWeight = Math.Round(newWeight, 1);
            OnStatusChanged($"模拟重量波动: {CurrentWeight} {CurrentUnit} (基准: {baseWeight}, 范围: ±{range})");
        }

        public void Stop()
        {
            if (!_isRunning) return;

            _isRunning = false;
            _cancellationTokenSource?.Cancel();

            try
            {
                _tcpListener?.Stop();
                OnStatusChanged("虚拟COM服务已停止");
            }
            catch (Exception ex)
            {
                OnStatusChanged($"停止虚拟COM服务时发生错误: {ex.Message}");
            }
        }

        private void OnStatusChanged(string message)
        {
            StatusChanged?.Invoke(this, $"[{DateTime.Now:HH:mm:ss}] {message}");
        }

        private void OnDataReceived(string data)
        {
            DataReceived?.Invoke(this, data);
        }

        public void Dispose()
        {
            Stop();
            _cancellationTokenSource?.Dispose();
        }
    }
}
