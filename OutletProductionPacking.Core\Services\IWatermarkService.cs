using System.Threading.Tasks;

namespace OutletProductionPacking.Core.Services
{
    public interface IWatermarkService
    {
        /// <summary>
        /// 为图片添加水印
        /// </summary>
        /// <param name="imagePath">图片路径</param>
        /// <param name="watermarkText">水印文本</param>
        /// <returns>是否成功</returns>
        Task<bool> AddWatermarkAsync(string imagePath, string watermarkText);
    }
}
