<Window x:Class="OutletProductionPacking.WPF.Views.EmergencyCloseWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views"
        mc:Ignorable="d"
        Title="紧急关闭窗口"
        Height="200"
        Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="紧急关闭导入窗口"
                   FontWeight="Bold"
                   FontSize="16"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"/>

        <TextBlock Grid.Row="1"
                   Text="如果导入窗口卡住无法关闭，点击下面的按钮强制关闭所有导入窗口。注意：这可能会导致数据不完整。"
                   TextWrapping="Wrap"
                   VerticalAlignment="Center"/>

        <Button Grid.Row="2"
                Content="强制关闭所有导入窗口"
                Click="CloseAllImportWindows_Click"
                HorizontalAlignment="Center"
                Padding="20,5"
                Margin="0,10,0,0"
                Background="#FFE0E0E0"
                Foreground="#FF6347"/>
    </Grid>
</Window>
