===============================================
手动使用com0com创建COM5虚拟串口
===============================================

由于脚本可能有路径问题，请按以下步骤手动操作：

方法1: 使用开始菜单
===============================================
1. 点击开始菜单
2. 搜索 "com0com"
3. 找到 "Setup Command Prompt" 或 "com0com Setup"
4. 右键选择"以管理员身份运行"
5. 在打开的命令窗口中输入：
   setupc install PortName=COM5 PortName=COM99
6. 按回车执行

方法2: 直接运行setupc.exe
===============================================
1. 打开文件资源管理器
2. 导航到以下路径之一：
   - C:\Program Files\com0com\
   - C:\Program Files (x86)\com0com\
3. 找到 setupc.exe 文件
4. 右键点击 setupc.exe
5. 选择"以管理员身份运行"
6. 在命令窗口中输入：
   install PortName=COM5 PortName=COM99

方法3: 使用命令提示符
===============================================
1. 按 Win+R 打开运行对话框
2. 输入 cmd 并按 Ctrl+Shift+Enter（以管理员身份运行）
3. 在命令提示符中输入以下命令之一：

   如果安装在Program Files：
   cd "C:\Program Files\com0com"
   setupc install PortName=COM5 PortName=COM99

   如果安装在Program Files (x86)：
   cd "C:\Program Files (x86)\com0com"
   setupc install PortName=COM5 PortName=COM99

验证创建结果
===============================================
执行完成后，输入以下命令查看结果：
setupc list

应该看到类似输出：
CNCA0 PortName=COM5
CNCB0 PortName=COM99

检查设备管理器
===============================================
1. 右键点击"此电脑"
2. 选择"管理"
3. 点击"设备管理器"
4. 展开"端口(COM和LPT)"
5. 应该看到：
   - com0com - serial port emulator (COM5)
   - com0com - serial port emulator (COM99)

如果仍有问题
===============================================
1. 确认com0com已正确安装
2. 重启计算机
3. 以管理员身份重新尝试
4. 检查是否有其他程序占用COM5

完成后
===============================================
COM5虚拟串口创建成功后：
1. 启动电子秤模拟器
2. 在您的程序中连接COM5
3. 应该可以正常通信了

===============================================
