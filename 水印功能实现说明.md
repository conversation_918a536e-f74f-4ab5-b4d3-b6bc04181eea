# 水印功能实现说明

## 📋 功能概述

实现了一个自动水印处理系统，在主软件运行期间，创建后台线程每隔30秒执行一次，自动为产品照片添加水印。

## 🚀 功能特性

### 1. **数据库字段**
- 在 `ProductPhotos` 表中添加了 `Watermarked` 字段（SMALLINT类型）
- 字段含义：
  - `0` = 未处理
  - `1` = 已添加水印
  - `2` = 处理失败

### 2. **自动处理逻辑**
- 每30秒执行一次水印处理任务
- 查询条件：`Watermarked = 0` 且 `CreatedAt < 当前时间-1分钟`
- 每次最多处理100条记录
- 按创建时间升序处理（先处理旧的照片）

### 3. **水印规格**
- **透明度**：70%（Alpha值179）
- **颜色**：黄色
- **字体**：Arial Bold
- **大小**：自动计算，使文本宽度占图片宽度的80%
- **位置**：居中显示
- **内容**：使用照片记录的Barcode值

### 4. **错误处理**
- 文件不存在：标记为处理失败（状态2）
- 水印添加失败：标记为处理失败（状态2）
- 处理成功：标记为已处理（状态1）

## 📁 文件结构

### 新增文件
```
OutletProductionPacking.Core/Services/
├── IWatermarkService.cs                    # 水印服务接口

OutletProductionPacking.Services/
├── WatermarkService.cs                     # 水印服务实现
└── WatermarkProcessorService.cs            # 水印处理后台服务

OutletProductionPacking.Data/Scripts/
└── AddWatermarkedFieldToProductPhotos.sql  # 数据库字段添加脚本
```

### 修改文件
```
OutletProductionPacking.Data/Models/
└── ProductPhoto.cs                         # 添加Watermarked字段

OutletProductionPacking.Data/
├── AppDbContext.cs                         # 配置Watermarked字段
└── Repositories/
    ├── IProductPhotoRepository.cs          # 添加水印相关方法
    └── ProductPhotoRepository.cs           # 实现水印相关方法

OutletProductionPacking.WPF/
└── App.xaml.cs                            # 注册服务并启动后台处理
```

## 🔧 技术实现

### 核心服务

#### 1. **IWatermarkService / WatermarkService**
- 负责具体的图片水印添加逻辑
- 使用 `System.Drawing` 进行图像处理
- 支持透明度、字体大小自动计算

#### 2. **WatermarkProcessorService**
- 后台定时服务，每30秒执行一次
- 查询未处理的照片记录
- 调用水印服务处理图片
- 更新处理状态

#### 3. **数据库扩展**
- 扩展 `ProductPhotoRepository` 添加查询未处理照片的方法
- 添加更新水印状态的方法

### 工作流程

1. **应用启动**：在 `App.OnStartup` 中启动 `WatermarkProcessorService`
2. **定时执行**：每30秒触发一次处理任务
3. **查询数据**：获取需要处理的照片记录（最多100条）
4. **处理照片**：
   - 检查文件是否存在
   - 调用水印服务添加水印
   - 更新数据库状态
5. **应用退出**：在 `App.OnExit` 中停止后台服务

## 📝 使用方法

### 1. **数据库更新**
执行SQL脚本添加Watermarked字段：
```sql
-- 运行 OutletProductionPacking.Data/Scripts/AddWatermarkedFieldToProductPhotos.sql
```

### 2. **自动启动**
- 主软件启动时自动启动水印处理服务
- 无需手动干预，后台自动处理

### 3. **监控状态**
可以通过查询数据库监控处理状态：
```sql
-- 查看处理统计
SELECT 
    Watermarked,
    COUNT(*) as Count,
    CASE 
        WHEN Watermarked = 0 THEN '未处理'
        WHEN Watermarked = 1 THEN '已处理'
        WHEN Watermarked = 2 THEN '处理失败'
    END as Status
FROM ProductPhotos 
GROUP BY Watermarked;
```

## 🔍 测试

### 测试程序
提供了 `WatermarkTest.cs` 测试程序，可以独立测试水印功能：
- 创建测试图片
- 添加水印
- 验证效果

### 运行测试
```bash
# 编译并运行测试程序
csc WatermarkTest.cs /r:System.Drawing.dll
WatermarkTest.exe
```

## ⚠️ 注意事项

1. **文件权限**：确保应用程序对照片文件有读写权限
2. **磁盘空间**：处理过程中会创建临时文件，确保有足够磁盘空间
3. **性能影响**：大量照片处理可能影响系统性能，已限制每次最多处理100条
4. **错误恢复**：处理失败的照片不会重复处理，需要手动重置状态或检查原因

## 🔄 维护

### 重新处理失败的照片
```sql
-- 将失败的照片重置为未处理状态
UPDATE ProductPhotos SET Watermarked = 0 WHERE Watermarked = 2;
```

### 查看处理日志
水印处理的详细日志会记录在应用程序日志中，可以通过日志查看处理详情和错误信息。
