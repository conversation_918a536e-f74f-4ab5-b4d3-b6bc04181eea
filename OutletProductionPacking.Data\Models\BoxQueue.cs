using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OutletProductionPacking.Data.Models
{
    /// <summary>
    /// 小盒装箱队列表（临时表）
    /// 存储等待装小盒的条码
    /// </summary>
    [Table("BoxQueue")]
    public class BoxQueue
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 产品条码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Barcode { get; set; } = string.Empty;

        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// 加入队列时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey("OrderId")]
        public virtual ProductionOrder Order { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
