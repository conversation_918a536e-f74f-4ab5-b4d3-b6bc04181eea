# 虚拟串口设置指南

## 概述
本文档介绍如何为电子秤模拟器创建真正可连接的虚拟COM端口，解决程序无法连接到COM5的问题。

## 问题描述
- 电子秤模拟器启动后，程序无法连接到COM5端口
- 系统中不存在COM5物理端口
- 需要创建一个真正可连接的虚拟串口

## 解决方案

### 方案1: 自动创建（推荐）
1. 启动电子秤模拟器
2. 程序会自动检测COM5是否存在
3. 如果不存在，会自动尝试创建虚拟串口
4. 创建成功后，程序可以正常连接

### 方案2: 手动创建
1. 以管理员身份运行 `Tools/setup_virtual_com.bat`
2. 脚本会尝试多种方法创建虚拟串口
3. 创建成功后重启电子秤模拟器

### 方案3: 使用第三方工具

#### com0com（推荐）
1. 下载并安装 com0com
   - 下载地址: https://sourceforge.net/projects/com0com/
2. 安装后运行 `setup_virtual_com.bat`
3. 工具会自动使用com0com创建虚拟串口对

#### VSPE (Virtual Serial Port Emulator)
1. 下载并安装 VSPE
   - 下载地址: https://www.eterlogic.com/Products.VSPE.html
2. 创建虚拟串口对 COM5 <-> COM99

## 验证方法

### 检查串口是否创建成功
1. 打开设备管理器
2. 展开"端口(COM和LPT)"
3. 查看是否有COM5端口

### 使用命令行检查
```cmd
# 列出所有串口
wmic path Win32_SerialPort get DeviceID,Name

# 测试COM5是否可用
mode COM5
```

### 程序测试
1. 启动电子秤模拟器
2. 在主程序中尝试连接COM5
3. 查看连接状态和日志信息

## 故障排除

### 问题1: 权限不足
**症状**: 创建虚拟串口失败，提示权限错误
**解决**: 以管理员身份运行程序或脚本

### 问题2: 端口仍然不存在
**症状**: 脚本执行成功但COM5仍不可用
**解决**: 
1. 重启计算机
2. 在设备管理器中刷新硬件
3. 检查Windows更新

### 问题3: 程序无法连接
**症状**: COM5存在但程序连接失败
**解决**:
1. 检查端口是否被其他程序占用
2. 重启电子秤模拟器
3. 检查串口参数（波特率、数据位等）

### 问题4: 虚拟串口不稳定
**症状**: 连接时断时续
**解决**:
1. 使用com0com等专业工具
2. 检查系统资源使用情况
3. 更新串口驱动程序

## 删除虚拟串口

如需删除创建的虚拟串口：
1. 以管理员身份运行 `Tools/remove_virtual_com.bat`
2. 或在设备管理器中手动删除设备
3. 重启计算机完成清理

## 技术原理

### 虚拟串口实现方式
1. **注册表方式**: 直接修改Windows注册表添加串口映射
2. **驱动方式**: 使用虚拟串口驱动程序
3. **软件模拟**: 使用第三方工具创建虚拟设备

### 程序集成
- `MockScaleService` 自动检测并创建虚拟串口
- `VirtualSerialPortService` 提供虚拟串口管理功能
- `WindowsVirtualComService` 提供Windows系统级虚拟串口支持

## 注意事项

1. **管理员权限**: 创建虚拟串口需要管理员权限
2. **系统兼容性**: 某些方法可能在不同Windows版本上表现不同
3. **安全软件**: 杀毒软件可能阻止注册表修改
4. **重启要求**: 某些情况下需要重启计算机才能生效
5. **端口冲突**: 确保COM5没有被其他设备占用

## 支持的Windows版本
- Windows 10 (推荐)
- Windows 11
- Windows Server 2016/2019/2022

## 联系支持
如果遇到问题，请提供以下信息：
- Windows版本
- 错误日志
- 设备管理器截图
- 程序运行日志
