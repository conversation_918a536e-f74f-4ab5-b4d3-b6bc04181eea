using Microsoft.EntityFrameworkCore;
using OutletProductionPacking.Core.DTOs;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Data;
using OutletProductionPacking.Data.Models;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace OutletProductionPacking.Services
{
    /// <summary>
    /// 生产明细查询服务实现
    /// </summary>
    public class ProductionDetailQueryService : IProductionDetailQueryService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public ProductionDetailQueryService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<OrderSummaryDto>> GetOrderSummaryAsync(ProductionDetailQueryParams queryParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from order in context.ProductionOrders
                        join product in context.Products on order.ProductCode equals product.Code
                        select new { order, product };

            // 应用查询条件
            if (!string.IsNullOrWhiteSpace(queryParams.OrderNumber))
            {
                query = query.Where(x => x.order.OrderNumber.Contains(queryParams.OrderNumber));
            }

            if (!string.IsNullOrWhiteSpace(queryParams.ProductCode))
            {
                query = query.Where(x => x.order.ProductCode.Contains(queryParams.ProductCode));
            }

            if (!string.IsNullOrWhiteSpace(queryParams.ProductName))
            {
                query = query.Where(x => x.order.ProductName.Contains(queryParams.ProductName));
            }

            if (queryParams.StartDate.HasValue)
            {
                query = query.Where(x => x.order.CreatedAt >= queryParams.StartDate.Value);
            }

            if (queryParams.EndDate.HasValue)
            {
                var endDate = queryParams.EndDate.Value.AddDays(1);
                query = query.Where(x => x.order.CreatedAt < endDate);
            }

            if (queryParams.IsFinished.HasValue)
            {
                query = query.Where(x => x.order.IsFinished == queryParams.IsFinished.Value);
            }

            query = query.OrderByDescending(x => x.order.CreatedAt);

            var orders = await query
                .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                .Take(queryParams.PageSize)
                .ToListAsync();

            var result = new List<OrderSummaryDto>();

            foreach (var item in orders)
            {
                var order = item.order;
                var product = item.product;

                // 获取统计数据
                var stats = await GetOrderStatisticsAsync(context, order.Id);

                var summary = new OrderSummaryDto
                {
                    OrderId = order.Id,
                    OrderNumber = order.OrderNumber,
                    ProductCode = order.ProductCode,
                    ProductName = order.ProductName,
                    ProductSpecification = order.ProductSpecification ?? product.Specification ?? string.Empty,
                    PlannedQuantity = order.Quantity,
                    ProducedQuantity = stats.ProducedQuantity,
                    QualifiedQuantity = stats.QualifiedQuantity,
                    UnqualifiedQuantity = stats.UnqualifiedQuantity,
                    QualificationRate = stats.ProducedQuantity > 0 ?
                        Math.Round((decimal)stats.QualifiedQuantity / stats.ProducedQuantity * 100, 2) : 0,
                    BoxedQuantity = stats.BoxedQuantity,
                    CartonedQuantity = stats.CartonedQuantity,
                    ProductionStartTime = stats.ProductionStartTime,
                    LastUpdateTime = stats.LastUpdateTime ?? order.CreatedAt,
                    OrderCreatedAt = order.CreatedAt,
                    IsFinished = order.IsFinished,
                    CompletionStatus = order.IsFinished ? "已完成" : "进行中"
                };

                result.Add(summary);
            }

            return result;
        }

        public async Task<int> GetOrderSummaryCountAsync(ProductionDetailQueryParams queryParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from order in context.ProductionOrders
                        join product in context.Products on order.ProductCode equals product.Code
                        select new { order, product };

            // 应用查询条件
            if (!string.IsNullOrWhiteSpace(queryParams.OrderNumber))
            {
                query = query.Where(x => x.order.OrderNumber.Contains(queryParams.OrderNumber));
            }

            if (!string.IsNullOrWhiteSpace(queryParams.ProductCode))
            {
                query = query.Where(x => x.order.ProductCode.Contains(queryParams.ProductCode));
            }

            if (!string.IsNullOrWhiteSpace(queryParams.ProductName))
            {
                query = query.Where(x => x.order.ProductName.Contains(queryParams.ProductName));
            }

            if (queryParams.StartDate.HasValue)
            {
                query = query.Where(x => x.order.CreatedAt >= queryParams.StartDate.Value);
            }

            if (queryParams.EndDate.HasValue)
            {
                var endDate = queryParams.EndDate.Value.AddDays(1);
                query = query.Where(x => x.order.CreatedAt < endDate);
            }

            if (queryParams.IsFinished.HasValue)
            {
                query = query.Where(x => x.order.IsFinished == queryParams.IsFinished.Value);
            }

            return await query.CountAsync();
        }

        public async Task<List<BarcodeDetailDto>> GetBarcodeDetailsAsync(BarcodeDetailQueryParams queryParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from barcode in context.ProductionOrderBarcodes
                        join order in context.ProductionOrders on barcode.OrderId equals order.Id
                        where barcode.OrderId == queryParams.OrderId
                        select new { barcode, order };

            // 应用条码查询条件
            if (!string.IsNullOrWhiteSpace(queryParams.Barcode))
            {
                query = query.Where(x => x.barcode.Barcode.Contains(queryParams.Barcode));
            }

            query = query.OrderBy(x => x.barcode.Barcode);

            var barcodes = await query
                .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                .Take(queryParams.PageSize)
                .ToListAsync();

            var result = new List<BarcodeDetailDto>();

            foreach (var item in barcodes)
            {
                var barcodeEntity = item.barcode;
                var order = item.order;

                // 获取质检信息
                var qualityInfo = await GetBarcodeQualityInfoAsync(context, barcodeEntity.Barcode);
                
                // 获取拍照信息
                var photoInfo = await GetBarcodePhotoInfoAsync(context, barcodeEntity.Barcode);
                
                // 获取装箱信息
                var packagingInfo = await GetBarcodePackagingInfoAsync(context, barcodeEntity.Barcode);

                var detail = new BarcodeDetailDto
                {
                    Barcode = barcodeEntity.Barcode,
                    OrderId = order.Id,
                    OrderNumber = order.OrderNumber,
                    ProductCode = order.ProductCode,
                    ProductName = order.ProductName,
                    QualityStatus = qualityInfo.Status,
                    QualityInspectionTime = qualityInfo.InspectionTime,
                    QualityOperator = qualityInfo.OperatorName,
                    PhotoStatus = photoInfo.Status,
                    PhotoTime = photoInfo.PhotoTime,
                    PhotoOperator = photoInfo.OperatorName,
                    PhotoPath = photoInfo.PhotoPath,
                    BoxingStatus = string.IsNullOrEmpty(barcodeEntity.BoxNumber) ? "未装盒" : "已装盒",
                    BoxNumber = barcodeEntity.BoxNumber ?? string.Empty,
                    BoxingTime = packagingInfo.BoxingTime,
                    CartonStatus = packagingInfo.CartonStatus,
                    CartonNumber = packagingInfo.CartonNumber,
                    CartonTime = packagingInfo.CartonTime,
                    IsProduced = barcodeEntity.IsProduced,
                    Remarks = string.Empty
                };

                result.Add(detail);
            }

            return result;
        }

        public async Task<int> GetBarcodeDetailsCountAsync(BarcodeDetailQueryParams queryParams)
        {
            using var context = await _contextFactory.CreateDbContextAsync();

            var query = from barcode in context.ProductionOrderBarcodes
                        join order in context.ProductionOrders on barcode.OrderId equals order.Id
                        where barcode.OrderId == queryParams.OrderId
                        select new { barcode, order };

            // 应用条码查询条件
            if (!string.IsNullOrWhiteSpace(queryParams.Barcode))
            {
                query = query.Where(x => x.barcode.Barcode.Contains(queryParams.Barcode));
            }

            return await query.CountAsync();
        }

        public async Task<List<User>> GetOperatorsAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Users
                .Where(u => u.IsActive)
                .OrderBy(u => u.Username)
                .ToListAsync();
        }

        public async Task<List<string>> GetProductCodesAsync()
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Products
                .Where(p => p.IsActive)
                .Select(p => p.Code)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();
        }

        // 获取订单统计数据的辅助方法
        private async Task<OrderStatistics> GetOrderStatisticsAsync(AppDbContext context, int orderId)
        {
            var stats = new OrderStatistics();

            // 获取已生产数量
            stats.ProducedQuantity = await context.ProductionOrderBarcodes
                .Where(b => b.OrderId == orderId && b.IsProduced)
                .CountAsync();

            // 获取质检统计
            var qualityStats = await (from b in context.ProductionOrderBarcodes
                                     join q in context.QualityInspections on b.Barcode equals q.Barcode into qualityGroup
                                     from quality in qualityGroup.DefaultIfEmpty()
                                     where b.OrderId == orderId && b.IsProduced
                                     group quality by b.Barcode into g
                                     select new
                                     {
                                         Barcode = g.Key,
                                         LatestResult = g.OrderByDescending(x => x.CreatedAt).FirstOrDefault()
                                     }).ToListAsync();

            stats.QualifiedQuantity = qualityStats.Count(x => x.LatestResult != null && x.LatestResult.Result);
            stats.UnqualifiedQuantity = qualityStats.Count(x => x.LatestResult != null && !x.LatestResult.Result);

            // 获取装盒数量
            stats.BoxedQuantity = await context.ProductionOrderBarcodes
                .Where(b => b.OrderId == orderId && !string.IsNullOrEmpty(b.BoxNumber))
                .CountAsync();

            // 获取装箱数量
            stats.CartonedQuantity = await (from b in context.ProductionOrderBarcodes
                                           join cbm in context.CartonBoxMappings on b.BoxNumber equals cbm.BoxNumber
                                           where b.OrderId == orderId && !string.IsNullOrEmpty(b.BoxNumber)
                                           select b.Barcode).CountAsync();

            // 获取生产开始时间（第一个质检记录的时间）
            stats.ProductionStartTime = await context.QualityInspections
                .Where(q => q.OrderId == orderId)
                .OrderBy(q => q.CreatedAt)
                .Select(q => (DateTime?)q.CreatedAt)
                .FirstOrDefaultAsync();

            // 获取最后更新时间
            var lastQualityTime = await context.QualityInspections
                .Where(q => q.OrderId == orderId)
                .OrderByDescending(q => q.CreatedAt)
                .Select(q => (DateTime?)q.CreatedAt)
                .FirstOrDefaultAsync();

            var lastPhotoTime = await context.ProductPhotos
                .Where(p => p.OrderId == orderId)
                .OrderByDescending(p => p.CreatedAt)
                .Select(p => (DateTime?)p.CreatedAt)
                .FirstOrDefaultAsync();

            stats.LastUpdateTime = new[] { lastQualityTime, lastPhotoTime }
                .Where(t => t.HasValue)
                .DefaultIfEmpty()
                .Max();

            return stats;
        }

        // 获取条码质检信息的辅助方法
        private async Task<BarcodeQualityInfo> GetBarcodeQualityInfoAsync(AppDbContext context, string barcode)
        {
            var latestQuality = await context.QualityInspections
                .Where(q => q.Barcode == barcode)
                .OrderByDescending(q => q.CreatedAt)
                .FirstOrDefaultAsync();

            if (latestQuality == null)
            {
                return new BarcodeQualityInfo { Status = "未检测" };
            }

            var operatorName = await context.Users
                .Where(u => u.Id == latestQuality.OperatorId)
                .Select(u => u.Username)
                .FirstOrDefaultAsync() ?? "未知";

            return new BarcodeQualityInfo
            {
                Status = latestQuality.Result ? "合格" : "不合格",
                InspectionTime = latestQuality.CreatedAt,
                OperatorName = operatorName
            };
        }

        // 获取条码拍照信息的辅助方法
        private async Task<BarcodePhotoInfo> GetBarcodePhotoInfoAsync(AppDbContext context, string barcode)
        {
            var photo = await context.ProductPhotos
                .Where(p => p.Barcode == barcode)
                .OrderByDescending(p => p.CreatedAt)
                .FirstOrDefaultAsync();

            if (photo == null)
            {
                return new BarcodePhotoInfo { Status = "未拍照" };
            }

            var operatorName = await context.Users
                .Where(u => u.Id == photo.OperatorId)
                .Select(u => u.Username)
                .FirstOrDefaultAsync() ?? "未知";

            return new BarcodePhotoInfo
            {
                Status = "已拍照",
                PhotoTime = photo.CreatedAt,
                OperatorName = operatorName,
                PhotoPath = photo.PhotoPath ?? string.Empty
            };
        }

        // 获取条码包装信息的辅助方法
        private async Task<BarcodePackagingInfo> GetBarcodePackagingInfoAsync(AppDbContext context, string barcode)
        {
            var barcodeEntity = await context.ProductionOrderBarcodes
                .Where(b => b.Barcode == barcode)
                .FirstOrDefaultAsync();

            var info = new BarcodePackagingInfo();

            if (barcodeEntity != null && !string.IsNullOrEmpty(barcodeEntity.BoxNumber))
            {
                // 获取装盒时间
                var boxPackage = await context.BoxPackages
                    .Where(bp => bp.BoxNumber == barcodeEntity.BoxNumber)
                    .FirstOrDefaultAsync();

                info.BoxingTime = boxPackage?.CreatedAt;

                // 检查是否已装箱
                var cartonMapping = await context.CartonBoxMappings
                    .Where(cbm => cbm.BoxNumber == barcodeEntity.BoxNumber)
                    .FirstOrDefaultAsync();

                if (cartonMapping != null)
                {
                    info.CartonStatus = "已装箱";
                    info.CartonNumber = cartonMapping.CartonNumber;
                    info.CartonTime = cartonMapping.CreatedAt;
                }
                else
                {
                    info.CartonStatus = "未装箱";
                }
            }
            else
            {
                info.CartonStatus = "未装箱";
            }

            return info;
        }

        // 导出订单汇总到Excel
        public async Task<bool> ExportOrderSummaryToExcelAsync(ProductionDetailQueryParams queryParams, string filePath)
        {
            try
            {
                // 获取所有订单汇总数据（不分页）
                var summaries = await GetOrderSummaryAsync(queryParams);

                // 创建工作簿
                var workbook = new XSSFWorkbook();
                var sheet = workbook.CreateSheet("订单汇总");

                // 创建表头样式
                var headerStyle = workbook.CreateCellStyle();
                var headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);
                headerStyle.FillForegroundColor = IndexedColors.Grey25Percent.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;

                // 添加表头
                var headerRow = sheet.CreateRow(0);
                var headers = new string[]
                {
                    "订单号", "产品编码", "产品名称", "规格型号", "订单数量",
                    "已生产数量", "已质检数量", "已拍照数量", "已装盒数量",
                    "已装箱数量", "完成状态", "创建时间"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    var cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                }

                // 添加数据
                int rowIndex = 1;
                foreach (var summary in summaries)
                {
                    var row = sheet.CreateRow(rowIndex);
                    row.CreateCell(0).SetCellValue(summary.OrderNumber);
                    row.CreateCell(1).SetCellValue(summary.ProductCode);
                    row.CreateCell(2).SetCellValue(summary.ProductName);
                    row.CreateCell(3).SetCellValue(summary.ProductSpecification);
                    row.CreateCell(4).SetCellValue(summary.PlannedQuantity);
                    row.CreateCell(5).SetCellValue(summary.ProducedQuantity);
                    row.CreateCell(6).SetCellValue(summary.QualifiedQuantity + summary.UnqualifiedQuantity);
                    row.CreateCell(7).SetCellValue(0); // 拍照数量需要从统计中获取
                    row.CreateCell(8).SetCellValue(summary.BoxedQuantity);
                    row.CreateCell(9).SetCellValue(summary.CartonedQuantity);
                    row.CreateCell(10).SetCellValue(summary.IsFinished ? "已完成" : "进行中");
                    row.CreateCell(11).SetCellValue(summary.OrderCreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
                    rowIndex++;
                }

                // 自动调整列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fileStream);
                }

                workbook.Close();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出订单汇总失败: {ex.Message}");
                return false;
            }
        }

        // 导出条码明细到Excel
        public async Task<bool> ExportBarcodeDetailsToExcelAsync(BarcodeDetailQueryParams queryParams, string filePath)
        {
            try
            {
                // 获取所有条码明细数据（不分页）
                var details = await GetBarcodeDetailsAsync(queryParams);

                // 创建工作簿
                var workbook = new XSSFWorkbook();
                var sheet = workbook.CreateSheet("条码明细");

                // 创建表头样式
                var headerStyle = workbook.CreateCellStyle();
                var headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);
                headerStyle.FillForegroundColor = IndexedColors.Grey25Percent.Index;
                headerStyle.FillPattern = FillPattern.SolidForeground;

                // 添加表头
                var headerRow = sheet.CreateRow(0);
                var headers = new string[]
                {
                    "条码", "订单号", "产品编码", "产品名称", "质检状态", "质检时间", "质检操作员",
                    "拍照状态", "拍照时间", "拍照操作员", "照片路径", "装盒状态", "盒号",
                    "装盒时间", "装箱状态", "箱号", "装箱时间"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    var cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                }

                // 添加数据
                int rowIndex = 1;
                foreach (var detail in details)
                {
                    var row = sheet.CreateRow(rowIndex);
                    row.CreateCell(0).SetCellValue(detail.Barcode);
                    row.CreateCell(1).SetCellValue(detail.OrderNumber);
                    row.CreateCell(2).SetCellValue(detail.ProductCode);
                    row.CreateCell(3).SetCellValue(detail.ProductName);
                    row.CreateCell(4).SetCellValue(detail.QualityStatus);
                    row.CreateCell(5).SetCellValue(detail.QualityInspectionTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "");
                    row.CreateCell(6).SetCellValue(detail.QualityOperator);
                    row.CreateCell(7).SetCellValue(detail.PhotoStatus);
                    row.CreateCell(8).SetCellValue(detail.PhotoTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "");
                    row.CreateCell(9).SetCellValue(detail.PhotoOperator);
                    row.CreateCell(10).SetCellValue(detail.PhotoPath);
                    row.CreateCell(11).SetCellValue(detail.BoxingStatus);
                    row.CreateCell(12).SetCellValue(detail.BoxNumber);
                    row.CreateCell(13).SetCellValue(detail.BoxingTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "");
                    row.CreateCell(14).SetCellValue(detail.CartonStatus);
                    row.CreateCell(15).SetCellValue(detail.CartonNumber);
                    row.CreateCell(16).SetCellValue(detail.CartonTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "");
                    rowIndex++;
                }

                // 自动调整列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fileStream);
                }

                workbook.Close();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出条码明细失败: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ExportCompleteReportToExcelAsync(ProductionDetailQueryParams orderQueryParams, string filePath)
        {
            try
            {
                // 获取订单汇总数据
                var summaries = await GetOrderSummaryAsync(orderQueryParams);

                // 创建工作簿
                var workbook = new XSSFWorkbook();

                // 创建订单汇总工作表
                var summarySheet = workbook.CreateSheet("订单汇总");

                // 复用订单汇总导出逻辑
                // 这里可以调用内部方法来避免重复代码

                // 为每个订单创建条码明细工作表（限制数量避免文件过大）
                foreach (var summary in summaries.Take(10))
                {
                    var detailParams = new BarcodeDetailQueryParams
                    {
                        OrderId = summary.OrderId,
                        PageNumber = 1,
                        PageSize = int.MaxValue
                    };

                    var details = await GetBarcodeDetailsAsync(detailParams);
                    if (details.Any())
                    {
                        var detailSheet = workbook.CreateSheet($"明细_{summary.OrderNumber}");
                        // 添加条码明细数据...
                    }
                }

                // 保存文件
                using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fileStream);
                }

                workbook.Close();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出完整报表失败: {ex.Message}");
                return false;
            }
        }

        // 辅助数据结构
        private class OrderStatistics
        {
            public int ProducedQuantity { get; set; }
            public int QualifiedQuantity { get; set; }
            public int UnqualifiedQuantity { get; set; }
            public int BoxedQuantity { get; set; }
            public int CartonedQuantity { get; set; }
            public DateTime? ProductionStartTime { get; set; }
            public DateTime? LastUpdateTime { get; set; }
        }

        private class BarcodeQualityInfo
        {
            public string Status { get; set; } = string.Empty;
            public DateTime? InspectionTime { get; set; }
            public string OperatorName { get; set; } = string.Empty;
        }

        private class BarcodePhotoInfo
        {
            public string Status { get; set; } = string.Empty;
            public DateTime? PhotoTime { get; set; }
            public string OperatorName { get; set; } = string.Empty;
            public string PhotoPath { get; set; } = string.Empty;
        }

        private class BarcodePackagingInfo
        {
            public DateTime? BoxingTime { get; set; }
            public string CartonStatus { get; set; } = string.Empty;
            public string CartonNumber { get; set; } = string.Empty;
            public DateTime? CartonTime { get; set; }
        }
    }
}
