@echo off
echo ================================
echo com0com Virtual COM Port Setup
echo ================================
echo.

REM Check admin rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Administrator rights required!
    echo Right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator rights confirmed
echo.

REM Find com0com installation
set SETUPC_PATH=""
if exist "C:\Program Files\com0com\setupc.exe" (
    set SETUPC_PATH="C:\Program Files\com0com\setupc.exe"
    echo Found com0com at: C:\Program Files\com0com\
) else if exist "C:\Program Files (x86)\com0com\setupc.exe" (
    set SETUPC_PATH="C:\Program Files (x86)\com0com\setupc.exe"
    echo Found com0com at: C:\Program Files (x86)\com0com\
) else (
    echo ERROR: com0com not found!
    echo Please install com0com from: https://sourceforge.net/projects/com0com/
    pause
    exit /b 1
)

echo.
echo Current com0com configuration:
%SETUPC_PATH% list
echo.

echo Creating COM5 virtual port pair for transparent simulation...
echo Command: setupc install PortName=COM5 PortName=COM99
echo.
echo 说明:
echo - 您的软件连接 COM5 (以为是真实电子秤)
echo - 模拟器连接 COM99 (模拟电子秤行为)
echo - 数据在COM5和COM99之间透明传输
echo.

REM Remove any existing configuration first
echo [Step 1] Removing existing configurations...
%SETUPC_PATH% remove 0 >nul 2>&1
%SETUPC_PATH% remove 1 >nul 2>&1
%SETUPC_PATH% remove 2 >nul 2>&1

echo [Step 2] Creating new virtual port pair...
%SETUPC_PATH% install PortName=COM5 PortName=COM99

if %errorLevel% equ 0 (
    echo.
    echo [SUCCESS] Virtual port pair created successfully!
    echo.
    echo New configuration:
    %SETUPC_PATH% list
    echo.
    echo ================================
    echo Setup Complete!
    echo ================================
    echo.
    echo Virtual ports created:
    echo - COM5 (for your application)
    echo - COM99 (internal pair, don't use directly)
    echo.
    echo What you can do now:
    echo 1. Start your scale simulator
    echo 2. Connect your application to COM5
    echo 3. The simulator will communicate through COM5
    echo.
    echo Note: COM5 and COM99 are connected internally.
    echo Data sent to COM5 will appear on COM99 and vice versa.
    echo.
) else (
    echo.
    echo [FAILED] Failed to create virtual port pair
    echo.
    echo Troubleshooting:
    echo 1. Make sure com0com is properly installed
    echo 2. Try running as administrator
    echo 3. Check if COM5 is already in use by another application
    echo 4. Restart computer and try again
    echo.
    echo Current configuration:
    %SETUPC_PATH% list
    echo.
)

echo Press any key to exit...
pause >nul
