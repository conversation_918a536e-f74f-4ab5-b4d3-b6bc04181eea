<Window x:Class="OutletProductionPacking.WPF.Views.Dialogs.BarcodeListDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OutletProductionPacking.WPF.Views.Dialogs"
        mc:Ignorable="d"
        Title="条码列表" 
        Height="500" 
        Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 订单信息 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="{Binding Order.OrderNumber, StringFormat={}订单号: {0}}" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBlock Text="{Binding Order.ProductName, StringFormat={}产品名称: {0}}" Margin="0,0,0,5"/>
            <TextBlock Text="{Binding Order.ProductSpecification, StringFormat={}产品型号: {0}}" Margin="0,0,0,5"/>
            <TextBlock Text="{Binding Order.Quantity, StringFormat={}计划数量: {0}}" Margin="0,0,0,5"/>
        </StackPanel>

        <!-- 搜索栏 -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <TextBlock Grid.Column="0" Text="搜索条码:" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBox Grid.Column="1" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
        </Grid>

        <!-- 条码列表 -->
        <DataGrid Grid.Row="2"
                  ItemsSource="{Binding Barcodes}"
                  SelectedItem="{Binding SelectedBarcode}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  SelectionMode="Single"
                  SelectionUnit="FullRow"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  CanUserSortColumns="True"
                  AlternatingRowBackground="#F5F5F5"
                  BorderThickness="1"
                  BorderBrush="#DDDDDD"
                  Margin="0,0,0,10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="条码" Binding="{Binding Barcode}" Width="*"/>
                <DataGridCheckBoxColumn Header="已投产" Binding="{Binding IsProduced}" Width="80"/>
                <DataGridTextColumn Header="创建时间" Binding="{Binding CreatedAt, StringFormat={}{0:yyyy-MM-dd HH:mm:ss}}" Width="150"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 按钮 -->
        <Button Grid.Row="3" 
                Content="关闭" 
                Command="{Binding CloseCommand}" 
                CommandParameter="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType={x:Type Window}}}"
                HorizontalAlignment="Right" 
                Padding="20,5"/>
    </Grid>
</Window>
