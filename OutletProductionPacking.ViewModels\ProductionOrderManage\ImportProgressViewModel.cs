using System.ComponentModel;

namespace OutletProductionPacking.ViewModels.ProductionOrderManage
{
    public class ImportProgressViewModel : INotifyPropertyChanged
    {
        private int _fileIndex;
        private int _fileCount;
        private int _progress;
        private string _statusText;
        private bool _isCompleted;
        private string _currentOrderNumber = string.Empty;
        private string _currentProductCode = string.Empty;
        private string _currentProductName = string.Empty;
        private int _currentBarcodeCount;
        private int _currentBarcodeIndex;
        private string _importStatus = "准备中...";

        public int FileIndex
        {
            get => _fileIndex;
            set
            {
                if (_fileIndex != value)
                {
                    _fileIndex = value;
                    OnPropertyChanged(nameof(FileIndex));
                }
            }
        }

        public int FileCount
        {
            get => _fileCount;
            set
            {
                if (_fileCount != value)
                {
                    _fileCount = value;
                    OnPropertyChanged(nameof(FileCount));
                }
            }
        }

        public int Progress
        {
            get => _progress;
            set
            {
                if (_progress != value)
                {
                    _progress = value;
                    OnPropertyChanged(nameof(Progress));
                }
            }
        }

        public string StatusText
        {
            get => _statusText;
            set
            {
                if (_statusText != value)
                {
                    _statusText = value;
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        public bool IsCompleted
        {
            get => _isCompleted;
            set
            {
                if (_isCompleted != value)
                {
                    _isCompleted = value;
                    OnPropertyChanged(nameof(IsCompleted));
                }
            }
        }

        public string CurrentOrderNumber
        {
            get => _currentOrderNumber;
            set
            {
                if (_currentOrderNumber != value)
                {
                    _currentOrderNumber = value;
                    OnPropertyChanged(nameof(CurrentOrderNumber));
                }
            }
        }

        public string CurrentProductCode
        {
            get => _currentProductCode;
            set
            {
                if (_currentProductCode != value)
                {
                    _currentProductCode = value;
                    OnPropertyChanged(nameof(CurrentProductCode));
                }
            }
        }

        public string CurrentProductName
        {
            get => _currentProductName;
            set
            {
                if (_currentProductName != value)
                {
                    _currentProductName = value;
                    OnPropertyChanged(nameof(CurrentProductName));
                }
            }
        }

        public int CurrentBarcodeCount
        {
            get => _currentBarcodeCount;
            set
            {
                if (_currentBarcodeCount != value)
                {
                    _currentBarcodeCount = value;
                    OnPropertyChanged(nameof(CurrentBarcodeCount));
                }
            }
        }

        public int CurrentBarcodeIndex
        {
            get => _currentBarcodeIndex;
            set
            {
                if (_currentBarcodeIndex != value)
                {
                    _currentBarcodeIndex = value;
                    OnPropertyChanged(nameof(CurrentBarcodeIndex));
                }
            }
        }

        public string ImportStatus
        {
            get => _importStatus;
            set
            {
                if (_importStatus != value)
                {
                    _importStatus = value;
                    OnPropertyChanged(nameof(ImportStatus));
                }
            }
        }

        public ImportProgressViewModel()
        {
            FileIndex = 0;
            FileCount = 0;
            Progress = 0;
            StatusText = "准备导入...";
            IsCompleted = false;
            CurrentOrderNumber = string.Empty;
            CurrentProductCode = string.Empty;
            CurrentProductName = string.Empty;
            CurrentBarcodeCount = 0;
            CurrentBarcodeIndex = 0;
            ImportStatus = "准备中...";
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
