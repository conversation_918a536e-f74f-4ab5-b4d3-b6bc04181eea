using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using OutletProductionPacking.Core.Services;
using OutletProductionPacking.Utils.Services;

namespace OutletProductionPacking.WPF.Services
{
    /// <summary>
    /// 打印服务守护者 - 简单的守护线程管理BarTenderPrintService
    /// </summary>
    public class PrintServiceDaemon : IDisposable
    {
        private readonly ILogService _logger;
        private readonly string _printServicePath;
        private readonly string _printServiceExe;
        private readonly Timer _watchTimer;
        private Process? _printServiceProcess;
        private bool _isDisposed = false;
        private readonly object _lockObject = new object();

        public PrintServiceDaemon(ILogService logger)
        {
            _logger = logger;
            
            // 构建打印服务路径
            string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _printServicePath = Path.Combine(appDirectory, "BarTenderPrintService");
            _printServiceExe = Path.Combine(_printServicePath, "BarTenderPrintService.exe");
            
            // 检查打印服务是否存在
            if (!Directory.Exists(_printServicePath))
            {
                _logger.Info("BarTenderPrintService文件夹不存在，跳过打印服务管理");
                return;
            }
            
            if (!File.Exists(_printServiceExe))
            {
                _logger.Warn($"打印服务程序不存在: {_printServiceExe}");
                return;
            }

            _logger.Info($"打印服务守护者启动，监控路径: {_printServiceExe}");
            
            // 创建5秒间隔的定时器
            _watchTimer = new Timer(CheckAndRestartService, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
        }

        /// <summary>
        /// 检查并重启服务的定时器回调
        /// </summary>
        private void CheckAndRestartService(object? state)
        {
            if (_isDisposed) return;

            lock (_lockObject)
            {
                try
                {
                    // 检查进程是否还在运行
                    bool isRunning = _printServiceProcess != null && !_printServiceProcess.HasExited;
                    
                    if (!isRunning)
                    {
                        // 清理旧进程对象
                        if (_printServiceProcess != null)
                        {
                            _printServiceProcess.Dispose();
                            _printServiceProcess = null;
                        }
                        
                        // 启动新进程
                        StartPrintService();
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "检查打印服务状态时发生异常");
                }
            }
        }

        /// <summary>
        /// 启动打印服务
        /// </summary>
        private void StartPrintService()
        {
            try
            {
                if (!File.Exists(_printServiceExe))
                {
                    return; // 文件不存在，静默返回
                }

                _logger.Info("启动打印服务...");

                var startInfo = new ProcessStartInfo
                {
                    FileName = _printServiceExe,
                    WorkingDirectory = _printServicePath,
                    UseShellExecute = true, // 使用Shell执行，显示窗口
                    CreateNoWindow = false // 显示控制台窗口
                };

                _printServiceProcess = Process.Start(startInfo);

                if (_printServiceProcess != null)
                {
                    _logger.Info($"打印服务已启动，PID: {_printServiceProcess.Id}");
                }
                else
                {
                    _logger.Error("启动打印服务失败");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "启动打印服务时发生异常");
            }
        }

        /// <summary>
        /// 停止打印服务
        /// </summary>
        public void StopPrintService()
        {
            lock (_lockObject)
            {
                try
                {
                    if (_printServiceProcess != null && !_printServiceProcess.HasExited)
                    {
                        _logger.Info("停止打印服务...");
                        
                        // 尝试优雅关闭
                        _printServiceProcess.CloseMainWindow();
                        
                        // 等待3秒
                        if (!_printServiceProcess.WaitForExit(3000))
                        {
                            // 强制终止
                            _printServiceProcess.Kill();
                            _printServiceProcess.WaitForExit(1000);
                        }
                        
                        _logger.Info("打印服务已停止");
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "停止打印服务时发生异常");
                }
                finally
                {
                    _printServiceProcess?.Dispose();
                    _printServiceProcess = null;
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;
            
            _isDisposed = true;
            
            // 停止定时器
            _watchTimer?.Dispose();
            
            // 停止打印服务
            StopPrintService();
            
            GC.SuppressFinalize(this);
        }
    }
}
